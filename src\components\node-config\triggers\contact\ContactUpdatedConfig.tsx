import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ConfigComponentProps } from '../../types';

const ContactUpdatedConfig: React.FC<ConfigComponentProps> = ({ config, setConfig }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Handle form submission
    const handleSubmit = async () => {
        setIsSubmitting(true);
        try {
            // Simulate API submission
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Update config with final values

            alert('Configuration saved successfully!');
        } catch (error) {
            alert('Failed to save configuration');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-4">
            <div>
                <h3 className="font-semibold text-gray-900">Contact Updated</h3>
                <p className="text-sm text-gray-500">Send a cheer message to contact whenever contact is updated in system even with other automation.</p>
            </div>


            <Button
                onClick={handleSubmit}
                className="w-full"
            >
                Confirm
            </Button>
        </div>
    );
};

export default ContactUpdatedConfig;