import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const AssignedStaffEvalConfig = ({ config, setConfig }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedStaff, setSelectedStaff] = useState('');
    const [bulkAction, setBulkAction] = useState('');
    const [staffOptions, setStaffOptions] = useState([]);

    // Fetch staff data
    const fetchStaff = async () => {
        setIsLoading(true);
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Sample staff data
            const staffData = [
                { value: 'john_doe', label: '<PERSON>' },
                { value: 'jane_smith', label: '<PERSON>' },
                { value: 'mike_johnson', label: '<PERSON>' },
                { value: 'sarah_wilson', label: 'Sarah <PERSON>' },
                { value: 'david_brown', label: '<PERSON> <PERSON>' },
                { value: 'emily_davis', label: 'Emily <PERSON>' },
                { value: 'robert_miller', label: '<PERSON> <PERSON>' },
                { value: 'lisa_garcia', label: 'Lisa <PERSON>' }
            ];
            
            setStaffOptions(staffData);
        } catch (error) {
            console.error('Failed to fetch staff:', error);
            alert('Failed to load staff data');
        } finally {
            setIsLoading(false);
        }
    };

    // Load staff data on component mount
    useEffect(() => {
        fetchStaff();
    }, []);

    // Handle bulk action
    const handleBulkAction = (action) => {
        setBulkAction(action);
    };

    // Handle form submission
    const handleSubmit = async () => {
        setIsSubmitting(true);
        try {
            // Validate selection
            if (!selectedStaff) {
                alert('Please select a staff member');
                return;
            }

            if (!bulkAction) {
                alert('Please select a bulk action');
                return;
            }

            // Simulate API submission
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Update config with final values
            const newConfig = {
                ...config,
                selectedStaff: selectedStaff,
                bulkAction: bulkAction
            };
            
            setConfig(newConfig);
            alert('Configuration saved successfully!');
        } catch (error) {
            alert('Failed to save configuration');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-4">
            {/* Header */}
            <div>
                <h3 className="font-semibold text-gray-900">Assigned Staff</h3>
                <p className="text-sm text-gray-500">
                    Configure staff assignment and bulk action settings for contact management.
                </p>
            </div>

            {/* Staff Selection */}
            <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                    Staff <span className="text-red-500">*</span>
                </Label>
                
                <Select
                    value={selectedStaff}
                    onValueChange={setSelectedStaff}
                    disabled={isLoading}
                >
                    <SelectTrigger>
                        <SelectValue placeholder={isLoading ? "Loading staff..." : "Select Staff"} />
                    </SelectTrigger>
                    <SelectContent>
                        {staffOptions.map(staff => (
                            <SelectItem key={staff.value} value={staff.value}>
                                {staff.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                
                {selectedStaff && (
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-700">
                            Selected: {staffOptions.find(s => s.value === selectedStaff)?.label}
                        </span>
                        <button
                            onClick={() => setSelectedStaff('')}
                            className="text-red-600 hover:text-red-800 p-1"
                        >
                            <X className="w-4 h-4" />
                        </button>
                    </div>
                )}
            </div>

            {/* Bulk Actions */}
            <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                    Move All Bottom Action (selected in red)
                </Label>
                <div className="flex gap-2 flex-wrap">
                    <Button
                        type="button"
                        variant={bulkAction === 'yes' ? 'default' : 'outline'}
                        onClick={() => handleBulkAction('yes')}
                        className={bulkAction === 'yes' ? 'bg-red-500 hover:bg-red-600 text-white' : ''}
                    >
                        Move all action to Yes node
                    </Button>
                    <Button
                        type="button"
                        variant={bulkAction === 'no' ? 'default' : 'outline'}
                        onClick={() => handleBulkAction('no')}
                        className={bulkAction === 'no' ? 'bg-gray-600 hover:bg-gray-700 text-white' : ''}
                    >
                        Move all action to No node
                    </Button>
                </div>
            </div>

            {/* Submit Button */}
            <Button
                onClick={handleSubmit}
                disabled={isSubmitting || isLoading}
                className="w-full"
            >
                {isSubmitting ? 'Saving...' : 'Confirm'}
            </Button>
        </div>
    );
};

export default AssignedStaffEvalConfig;