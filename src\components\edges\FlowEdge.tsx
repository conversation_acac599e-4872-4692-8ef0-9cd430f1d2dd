import React, { useState, useEffect, useRef } from 'react';
import { EdgeLabelRenderer } from '@xyflow/react';
import { Plus, ChevronDown } from 'lucide-react';
import { useGraphStore } from '../../store/useGraphStore';
import { createPortal } from 'react-dom';
import { ActionCategoryModal } from '../ActionCategoryModal';
import { NodeData } from '@/data/types';
import { useWorkflowStore } from '@/hooks/useWorkflowState';
import { useCopyPaste } from '@/hooks/useCopyPaste';

interface FlowEdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  data?: {
    parentId?: string;
    beforeNodeId?: string;
  };
  sourcePosition?: string;
  targetPosition?: string;
  style?: React.CSSProperties;
}

const FlowEdge: React.FC<FlowEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  style = {},
  data,
}) => {
  const isHorizontal = false;
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Copy-paste state
  const { isCopy, isCut } = useWorkflowStore();
  const { pasteFlow, pasteCutFlow } = useCopyPaste();

  // Debug logging (can be removed in production)
  // console.log('🔍 FlowEdge copy state:', { isCopy, isCut, hasCopiedContent: isCopy || isCut });

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  let edgePath: string;
  let arrowPath: string;
  let centerX: number;
  let centerY: number;

  const parentId = data?.parentId;
  const beforeNodeId = data?.beforeNodeId;
  const hasCopiedContent = isCopy || isCut;

  const insertNode = useGraphStore((state) => state.insertNode);

  // Debug logs (can be removed in production)
  // console.log('FlowEdge rendered with:', { parentId, beforeNodeId, showActionModal, hasCopiedContent });

  if (isHorizontal) {
    edgePath = `M ${sourceX},${sourceY} L ${targetX},${targetY}`;
    centerX = (sourceX + targetX) / 2;
    centerY = (sourceY + targetY) / 2;
    const arrowSize = 6;
    arrowPath = `M ${targetX - arrowSize},${targetY - arrowSize} L ${targetX},${targetY} L ${targetX - arrowSize},${targetY + arrowSize}`;
  } else {
    edgePath = `M ${sourceX},${sourceY} L ${sourceX},${targetY}`;
    centerX = sourceX;
    centerY = (sourceY + targetY) / 2;
    const arrowSize = 6;
    arrowPath = `M ${sourceX - arrowSize},${targetY - arrowSize} L ${sourceX},${targetY} L ${sourceX + arrowSize},${targetY - arrowSize}`;
  }

  const handlePlusClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (hasCopiedContent) {
      // Show dropdown with paste option
      setShowDropdown(true);
    } else {
      // Normal behavior - show action modal
      setShowActionModal(true);
    }
  };

  const handleAddNode = () => {
    setShowDropdown(false);
    setShowActionModal(true);
  };

  const handlePasteFlow = () => {
    if (!parentId || !beforeNodeId) {
      console.error('Missing parentId or beforeNodeId for paste operation');
      return;
    }

    console.log('🔍 Pasting flow at edge:', { parentId, beforeNodeId });

    // For now, let's use the graph store's insertNode function
    // TODO: Implement proper paste functionality with graph store
    if (isCopy || isCut) {
      // Get copied data from workflow store
      const { copiedNodes, cutNodes } = useWorkflowStore.getState();
      const nodesToPaste = isCut ? cutNodes : copiedNodes;

      if (nodesToPaste && nodesToPaste.length > 0) {
        // For now, just paste the first node as an action
        const firstNode = nodesToPaste[0];
        console.log('🔍 Pasting node:', { firstNode, parentId, beforeNodeId });

        insertNode({
          type: firstNode.type || 'action',
          parentId,
          beforeNodeId,
          actionData: firstNode.data
        });

        console.log('🔍 Node inserted, clearing copy state');

        // Clear copy/cut state
        if (isCut) {
          useWorkflowStore.getState().forceResetCutState();
        } else {
          useWorkflowStore.getState().forceResetCopyState();
        }

        console.log('🔍 Copy state cleared');
      }
    }

    setShowDropdown(false);
  };

  const handleActionSelection = (action: NodeData) => {
    if (!parentId || !beforeNodeId) {
      console.error('Missing parentId or beforeNodeId:', { parentId, beforeNodeId });
      return;
    }

    console.log('Adding action:', action.label, 'before:', beforeNodeId, 'with parent:', parentId);

    // Determine node type from action
    const nodeType = action.type === 'condition' ? 'condition' : 'action';

    insertNode({
      type: nodeType,
      parentId,
      beforeNodeId,
      actionData: action
    });

    setShowActionModal(false); // Close modal after inserting node
  };

  return (
    <>
      {/* Line */}
      <path
        id={id}
        d={edgePath}
        stroke="#9CA3AF"
        strokeWidth={1.5}
        fill="none"
        style={style}
      />

      {/* Arrow */}
      <path
        d={arrowPath}
        stroke="#9CA3AF"
        strokeWidth={1.5}
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
      />

      {/* Plus Button */}
      <EdgeLabelRenderer>
        <div
          className="pointer-events-auto absolute z-10"
          style={{
            transform: `translate(-50%, -50%) translate(${centerX}px, ${centerY}px)`,
          }}
        >
          {hasCopiedContent ? (
            // Dropdown button when there's copied content
            <div className="relative" ref={dropdownRef}>
              <button
                className="w-8 h-5 bg-blue-500 border border-blue-600 rounded-md flex items-center justify-center hover:bg-blue-600 transition-colors shadow-sm cursor-pointer"
                onClick={handlePlusClick}
                onMouseDown={(e) => e.stopPropagation()}
                onMouseUp={(e) => e.stopPropagation()}
              >
                <ChevronDown className="w-4 h-4 text-white stroke-[2.5]" />
              </button>

              {/* Dropdown Menu */}
              {showDropdown && (
                <div className="absolute top-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[120px]">
                  <button
                    onClick={handleAddNode}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 border-b border-gray-100"
                  >
                    Add Node
                  </button>
                  <button
                    onClick={handlePasteFlow}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-blue-600"
                  >
                    {isCut ? 'Move Here' : 'Paste Flow'}
                  </button>
                </div>
              )}
            </div>
          ) : (
            // Normal plus button
            <button
              className="w-6 h-5 bg-gray-400 border border-gray-500 rounded-md flex items-center justify-center hover:bg-gray-500 transition-colors shadow-sm cursor-pointer"
              onClick={handlePlusClick}
              onMouseDown={(e) => e.stopPropagation()}
              onMouseUp={(e) => e.stopPropagation()}
            >
              <Plus className="w-4 h-4 text-white stroke-[2.5]" />
            </button>
          )}
        </div>
      </EdgeLabelRenderer>

      {/* Action Category Modal */}
      {showActionModal && createPortal(
        <ActionCategoryModal
          isOpen={showActionModal}
          onClose={() => setShowActionModal(false)}
          onSelectAction={handleActionSelection}
        />,
        document.body
      )}
    </>
  );
};

export default FlowEdge;