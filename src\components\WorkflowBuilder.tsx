
import React, { useCallback, useEffect, useState, useRef } from 'react';
import { Node } from '@xyflow/react';
// Removed unused zoom control icons - using React Flow controls now

import { NodeConfigPanel } from './node-config/NodeConfigPanel';
import { WorkflowHeader } from './WorkflowHeader';
import { TriggerCategoryModal } from './TriggerCategoryModal';
import { ActionCategoryModal } from './ActionCategoryModal';
import { BranchSelectionModal } from './modals/BranchSelectionModal';
import { RunsPanel } from './panels/RunsPanel';
import { VersionsPanel } from './panels/VersionsPanel';
import { PublishPanel } from './panels/PublishPanel';
import { useWorkflowStore } from '@/hooks/useWorkflowState';
import { useWorkflowActions } from '@/hooks/useWorkflowActions';
import { useWorkflowJSON } from '@/hooks/useWorkflowJSON';
import { useGraphStore } from '@/store/useGraphStore';
import { NodeData } from '@/data/types';
import { toast } from 'sonner';
import WorkFlowCanvas  from './WorkFlowCanvas';
import { getLayoutedElements } from '@/utils/dagreFunction';

export const WorkflowBuilder = () => {
  // Graph-based state management
  const nodeMap = useGraphStore((state) => state.nodes);
  const addNode = useGraphStore((state) => state.addNode);
  const removeNode = useGraphStore((state) => state.removeNode);
  const insertNode = useGraphStore((state) => state.insertNode);
  const reset = useGraphStore((state) => state.reset);

  // Keep some UI state in useWorkflowStore for compatibility
  const {
    selectedNode,
    setSelectedNode,
    workflowName,
    setWorkflowName,
    isActive,
    setIsActive,
    layoutDirection,
    setLayoutDirection,
  } = useWorkflowStore();

  const { executeWorkflow, saveWorkflow } = useWorkflowActions();

  const nodeWidth = 280;
  const nodeHeight = 60;

  // Helper functions for nested branch management
  const createBranchPath = useCallback((parentPath: string | undefined, conditionId: string, branchType: 'yes' | 'no'): string => {
    if (!parentPath) return `${conditionId}.${branchType}`;
    return `${parentPath}.${conditionId}.${branchType}`;
  }, []);

  // Helper function to check if a node is a "Remove Workflow" or "Exit Workflow" node
  const isRemoveWorkflowNode = useCallback((nodeId: string): boolean => {
    return nodeId === 'remove-workflow-action' || nodeId === 'exit-workflow-operation-action';
  }, []);

  // Helper function to validate if Remove Workflow node can be placed at a position
  const canPlaceRemoveWorkflowNode = useCallback((insertIndex: number, branchInfo?: any): boolean => {
    console.log('🔍 Validating Remove Workflow placement:', { insertIndex, branchInfo });

    // If it's in a conditional branch
    if (branchInfo) {
      const { conditionNodeId, branchType, placeholderNodeId } = branchInfo;

      // If it's a placeholder click, always allow Remove Workflow
      // This handles the case where user clicks on "No" branch placeholder
      if (placeholderNodeId && placeholderNodeId.startsWith('placeholder-')) {
        console.log('🔍 Placeholder click detected - allowing Remove Workflow');
        return true;
      }

      // If it's a plus button click within a branch (after existing nodes)
      // This handles clicking plus button on edges like "Send WhatsApp" -> Ghost Node
      if (placeholderNodeId && placeholderNodeId.startsWith('after-')) {
        console.log('🔍 Plus button click in branch detected - allowing Remove Workflow');
        return true;
      }

      // Find all nodes in this specific branch
      const allNodes = Object.values(nodeMap);
      const branchNodes = allNodes.filter(node =>
        node.data?.conditionNodeId === conditionNodeId &&
        node.data?.branchType === branchType &&
        node.type !== 'placeholder'
      );

      console.log('🔍 Branch nodes found:', branchNodes.map(n => ({ id: n.id, label: n.data?.label })));

      // Remove Workflow can only be added if there are no nodes after it in the branch
      // This means it should be the last node in the branch
      return branchNodes.length === 0; // Only allow if branch is empty (will be the last node)
    }

    // For main flow - check if this would be the last position
    const allNodes = Object.values(nodeMap);
    const flowNodes = allNodes.filter(node =>
      node.type !== 'placeholder' &&
      node.id !== 'end-1' &&
      !node.id.startsWith('placeholder-') &&
      !node.id.startsWith('trigger-') &&
      !node.data?.branchType // Exclude branch nodes
    );

    console.log('🔍 Main flow nodes:', flowNodes.map(n => ({ id: n.id, label: n.data?.label })));
    console.log('🔍 Insert index:', insertIndex, 'Flow nodes length:', flowNodes.length);

    // Remove Workflow can only be added at the end of main flow
    return insertIndex >= flowNodes.length;
  }, [nodeMap]);

  const parseBranchPath = useCallback((branchPath: string) => {
    const parts = branchPath.split('.');
    const conditions = [];
    for (let i = 0; i < parts.length; i += 2) {
      if (parts[i] && parts[i + 1]) {
        conditions.push({
          conditionId: parts[i],
          branchType: parts[i + 1] as 'yes' | 'no'
        });
      }
    }
    return conditions;
  }, []);

  const getParentConditions = useCallback((branchPath: string): string[] => {
    const conditions = parseBranchPath(branchPath);
    return conditions.map(c => c.conditionId);
  }, [parseBranchPath]);

  const getBranchLevel = useCallback((branchPath: string): number => {
    return parseBranchPath(branchPath).length;
  }, [parseBranchPath]);

  const createGhostNodeId = useCallback((branchPath: string): string => {
    return `ghost-${branchPath.replace(/\./g, '-')}`;
  }, []);

  // Modal states
  const [showTriggerModal, setShowTriggerModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionInsertIndex, setActionInsertIndex] = useState<number | null>(null);

  // Branch selection modal state for conditional copy-paste
  const [showBranchSelectionModal, setShowBranchSelectionModal] = useState(false);
  const [pendingConditionalPaste, setPendingConditionalPaste] = useState<{
    insertIndex: number;
    aboveNodeId: string;
    belowNodeId: string;
    downstreamNodeCount: number;
  } | null>(null);


  // Removed isReplacementMode - simplified approach

  // Panel states - only one panel can be open at a time
  const [activePanel, setActivePanel] = useState<'runs' | 'versions' | 'publish' | null>(null);

  // Enhanced state for nested condition branch management
  const [conditionBranchInfo, setConditionBranchInfo] = useState<{
    conditionNodeId: string;
    branchType: 'yes' | 'no';
    placeholderNodeId: string;
    insertionIndex?: number;
    branchPath?: string; // e.g., "condition1.yes.condition2.no" for nested tracking
    level?: number; // Nesting level (0 = root, 1 = first level, etc.)
    parentConditions?: string[]; // Array of parent condition IDs for context
  } | null>(null);



  // JSON generation (auto-updates on node changes)
  const { generateJSON } = useWorkflowJSON();

  // Debug: Log JSON when nodes change (for development)
  useEffect(() => {
    const allNodes = Object.values(nodeMap);
    if (allNodes.length > 0) {
      const json = generateJSON();
      // console.log('🔄 Current Workflow JSON:', json);
    }
  }, [Object.keys(nodeMap).length, generateJSON]);


  // Open action modal for insertion
  const openActionModal = useCallback((insertIndex?: number) => {
    console.log('🔍 openActionModal called with insertIndex:', insertIndex);
    // Get current nodes from store to avoid dependency loop
    const currentNodes = useWorkflowStore.getState().nodes;
    console.log('🔍 Current nodes:', currentNodes.map((n, i) => `${i}: ${n.id} (${n.data?.label || n.type})`));

    // Filter to get flow nodes only (like in useWorkflowGraph)
    const flowNodes = currentNodes.filter(node =>
      node.type !== 'placeholder' &&
      node.id !== 'virtual-end' &&
      !node.id.startsWith('placeholder-')
    );
    console.log('🔍 Flow nodes:', flowNodes.map((n, i) => `${i}: ${n.id} (${n.data?.label || n.type})`));
    console.log('🔍 Will insert after flow node at index:', insertIndex);

    setActionInsertIndex(insertIndex ?? null);
    setShowActionModal(true);
  }, []);

  // Handle conditional paste request
  const handleConditionalPasteRequest = useCallback((insertIndex: number, aboveNodeId: string, belowNodeId: string, downstreamCount: number) => {
    setPendingConditionalPaste({
      insertIndex,
      aboveNodeId,
      belowNodeId,
      downstreamNodeCount: downstreamCount
    });
    setShowBranchSelectionModal(true);
  }, []);

  // Core graph operations using useGraphStore
  const deleteSingleNode = useCallback((nodeId: string) => {
    console.log('🔍 Graph-based deleteSingleNode:', nodeId);
    removeNode(nodeId);
  }, [removeNode]);

  const deleteNodeSubtree = useCallback((nodeId: string) => {
    console.log('🔍 Graph-based deleteNodeSubtree:', nodeId);
    // For now, just delete the single node - TODO: implement subtree deletion
    removeNode(nodeId);
  }, [removeNode]);

  // Temporary placeholders for removed functionality
  const clearCopyState = () => {};
  const isCopy = false;
  const clearCutState = () => {};
  const isCut = false;
  const pasteConditionalFlow = () => {};

  // TODO: Re-implement edge fixing with graph store
  /*
  useEffect(() => {
    setEdges(currentEdges => {
      return currentEdges.map((edge, edgeIndex) => {
        // Fix ALL flowEdge type edges that don't have proper onOpenActionModal
        if (edge.type === 'flowEdge' && (!edge.data?.onOpenActionModal || edge.data.onOpenActionModal.toString().includes('Plus clicked:'))) {
          // Use the existing index if available, otherwise calculate it
          let insertIndex = edge.data?.index;

          if (insertIndex === undefined || insertIndex === null) {
            // Calculate the correct index based on the edge's position in the workflow
            const sourceNode = nodes.find(n => n.id === edge.source);
            if (sourceNode) {
              const sourceIndex = nodes.findIndex(n => n.id === sourceNode.id);
              insertIndex = sourceIndex >= 0 ? sourceIndex + 1 : edgeIndex;
            } else {
              insertIndex = edgeIndex;
            }
          }

          console.log('🔍 Fixing edge:', edge.id, 'using insertIndex:', insertIndex);

          return {
            ...edge,
            data: {
              ...edge.data,
              // Use the SAME pattern as duplication - direct function reference
              onOpenActionModal: (clickedIndex: number) => {
                console.log('🔍 Plus clicked from fixed edge:', edge.id, 'using stored insertIndex:', insertIndex);
                openActionModal(Number(insertIndex)); // Use the stored insertIndex, not the clicked one
              },
              // Preserve the calculated index
              index: insertIndex
            }
          };
        }
        return edge;
      });
    });
  }, [edges.length, nodes.length, openActionModal]); // Run when edges or nodes change
  */

  // Handle branch selection for conditional paste
  const handleBranchSelection = useCallback((selectedBranch: 'yes' | 'no') => {
    if (pendingConditionalPaste) {
      pasteConditionalFlow(
        pendingConditionalPaste.insertIndex,
        pendingConditionalPaste.aboveNodeId,
        pendingConditionalPaste.belowNodeId,
        selectedBranch
      );
      setPendingConditionalPaste(null);
    }
    setShowBranchSelectionModal(false);
  }, [pendingConditionalPaste, pasteConditionalFlow]);



  const handleNodeDeletion = useCallback((nodeId: string | number) => {
    const nodeIdStr = String(nodeId);

    if (nodeIdStr === 'virtual-end' || nodeIdStr.startsWith('trigger-')) {
      return;
    }

    console.log('🔍 Graph-based single node deletion:', nodeIdStr);

    // Check if the node being deleted is currently selected and close config panel
    if (selectedNode && selectedNode.id === nodeIdStr) {
      console.log('🔍 Closing config panel for deleted node:', nodeIdStr);
      setSelectedNode(null);
    }

    // Use deleteSingleNode for regular nodes (only deletes the node, not the entire subtree)
    deleteSingleNode(nodeIdStr);

    toast.success('Node deleted successfully!');
    console.log('✅ Single node deleted successfully using graph operations');
  }, [deleteSingleNode, selectedNode, setSelectedNode]);
  // Placeholder for condition deletion functions - will be defined after handleAddNodeToBranch

  // Handle replacement of condition node (keeps branches, replaces condition)
  const handleConditionNodeReplacement = useCallback((nodeId: string) => {
    console.log('🔍 Replacing condition node:', nodeId);

    // Set the condition node for replacement and open action modal
    setConditionBranchInfo({
      conditionNodeId: nodeId,
      branchType: 'yes', // This won't be used for replacement
      placeholderNodeId: nodeId // Use the condition node ID as placeholder
    });

    setShowActionModal(true);
    toast.info('Select a new condition to replace the current one');
  }, [setConditionBranchInfo, setShowActionModal]);

  // Handle deletion of entire condition node (deletes condition + all branches) - GRAPH-BASED VERSION
  const handleConditionNodeDeletion = useCallback((nodeId: string) => {
    console.log('🔍 Graph-based condition node deletion:', nodeId);

    // Check if the node being deleted is currently selected and close config panel
    if (selectedNode && selectedNode.id === nodeId) {
      console.log('🔍 Closing config panel for deleted condition node:', nodeId);
      setSelectedNode(null);
    }

    // Use the graph-based deletion which handles all the complex logic automatically
    deleteNodeSubtree(nodeId);

    toast.success('Condition node and all branches deleted!');
    console.log('✅ Condition node deleted successfully using graph operations');
  }, [deleteNodeSubtree, selectedNode, setSelectedNode]);

  // Simple node addition handler using graph store
  const handleNodeSelection = (nodeType: string, nodeData: NodeData, shouldAutoOpenConfig: boolean = false) => {
    console.log('🔍 handleNodeSelection called:', {
      nodeType,
      label: nodeData.label,
      shouldAutoOpenConfig
    });

    const newNodeId = `${nodeType}-${Date.now()}`;

    // Find the trigger and end nodes
    const allNodes = Object.values(nodeMap);
    const triggerNode = allNodes.find(node => node.type === 'trigger');
    const endNode = allNodes.find(node => node.type === 'endNode');

    if (!triggerNode || !endNode) {
      console.error('Trigger or end node not found');
      return;
    }

    // Create the new node
    const newGraphNode = {
      id: newNodeId,
      type: nodeType as any,
      position: { x: 100, y: 175 }, // Between trigger and end
      data: {
        ...nodeData,
        label: nodeData.label,
        isConfigured: false,
      },
      children: [endNode.id],
      parent: triggerNode.id,
    };

    // Add the new node to the graph
    addNode(newGraphNode);

    // Update trigger to point to new node instead of end
    const updatedTrigger = {
      ...triggerNode,
      children: [newNodeId]
    };

    // Remove and re-add trigger with updated children
    removeNode(triggerNode.id);
    addNode(updatedTrigger);

    // Auto-open config if requested
    if (shouldAutoOpenConfig) {
      setTimeout(() => {
        const newNodeFromStore = Object.values(useGraphStore.getState().nodes).find(n => n.id === newNodeId);
        if (newNodeFromStore) {
          setSelectedNode(newNodeFromStore as any);
        }
      }, 100);
    }

    console.log('✅ Node added successfully:', newNodeId);
  };

  // TODO: Implement other node operations with graph store
  const handleNodeInsertion = () => {
    console.log('TODO: Implement handleNodeInsertion with graph store');
  };

  const handleNodeDuplication = () => {
    console.log('TODO: Implement handleNodeDuplication with graph store');
  };

  const handleConditionNodeDeletion = () => {
    console.log('TODO: Implement handleConditionNodeDeletion with graph store');
  };

  // Simplified initialization
  useEffect(() => {
          // Find the edge that currently points to virtual-end
          const edgeToEnd = prevEdges.find(edge => edge.target === 'virtual-end');

          if (edgeToEnd) {
            // Remove the edge to virtual-end
            const updatedEdges = prevEdges.filter(edge => edge.target !== 'virtual-end');

            // Connect the previous node to the new node
            updatedEdges.push({
              id: `edge-${edgeToEnd.source}-${newNode.id}`,
              source: edgeToEnd.source,
              target: newNode.id,
              type: 'flowEdge',
              data: {
                onOpenActionModal: (insertIndex: number) => {
                  console.log('🔍 Plus clicked (new)', insertIndex);
                  openActionModal(insertIndex);
                },
                index: updatedNodes.length - 2,
              },
            });

            // ✅ Only connect to virtual-end if it's NOT a condition node
            if (!isConditionNode) {
              updatedEdges.push({
                id: `edge-${newNode.id}-virtual-end`,
                source: newNode.id,
                target: 'virtual-end',
                type: 'flowEdge',
                data: {
                  onOpenActionModal: (insertIndex: number) => {
                    openActionModal(updatedNodes.length - 1);
                  },
                  index: updatedNodes.length - 1,
                },
              });
              console.log('🔗 Connected new action node to flow:', `${edgeToEnd.source} -> ${newNode.id} -> virtual-end`);
            } else {
              console.log('🔗 Condition node added - NOT connecting to virtual-end:', newNode.id);
            }

            return updatedEdges;
          } else {
            // Fallback: if no edge to virtual-end exists, just connect new node to virtual-end
            const updatedEdges = [...prevEdges];

            // ✅ Only connect to virtual-end if it's NOT a condition node
            if (!isConditionNode) {
              updatedEdges.push({
                id: `edge-${newNode.id}-virtual-end`,
                source: newNode.id,
                target: 'virtual-end',
                type: 'flowEdge',
                data: {
                  onOpenActionModal: (insertIndex: number) => {
                    openActionModal(updatedNodes.length - 1);
                  },
                  index: updatedNodes.length - 1,
                },
              });
              console.log('🔗 Fallback: Connected new action node directly to virtual-end');
            } else {
              console.log('🔗 Fallback: Condition node added - NOT connecting to virtual-end:', newNode.id);
            }

            return updatedEdges;
          }
        });
      }

      return updatedNodes;
    });



    toast.success(`${nodeData.label} added to workflow!`);

    // ✅ Auto-open configuration panel for all nodes
    if (shouldAutoOpenConfig && (nodeType === 'action' || nodeType === 'condition' || !nodeType)) {
      console.log('🔍 Auto-opening config for node:', newNodeId, nodeData.label);
      
      if (isConditionNode) {
        // For condition nodes using graph operations, wait a bit longer
        setTimeout(() => {
          console.log('🔍 Finding condition node after graph operation:', newNodeId);
          const currentNodes = useWorkflowStore.getState().nodes;
          const addedNode = currentNodes.find(n => n.id === newNodeId);
          if (addedNode) {
            console.log('🔍 Found condition node, setting as selected:', addedNode.id);
            setSelectedNode(addedNode);
          } else {
            console.log('❌ Condition node not found in state:', newNodeId);
          }
        }, 200); // Longer timeout for graph operations
      } else {
        // Use setTimeout to ensure the node is added to state before opening config
        setTimeout(() => {
          console.log('🔍 Finding and setting selected node:', newNodeId);
          // Get the latest nodes from the store and find the newly added node
          const currentNodes = useWorkflowStore.getState().nodes;
          const addedNode = currentNodes.find(n => n.id === newNodeId);
          if (addedNode) {
            console.log('🔍 Found node, setting as selected:', addedNode.id);
            setSelectedNode(addedNode);
          } else {
            console.log('❌ Node not found in state:', newNodeId);
          }
        }, 100); // Increased timeout to ensure state update
      }
    }
  };

  // ✅ Removed useCallback to avoid circular dependencies
  const handleAddNodeToBranch = useCallback((branchType: string, placeholderNodeId: string, conditionNodeId: string, existingBranchPath?: string) => {
    console.log('🔍 🎯 PLACEHOLDER CLICKED - handleAddNodeToBranch called:', { branchType, placeholderNodeId, conditionNodeId, existingBranchPath });

    // Create or extend the branch path for nested conditions
    const branchPath = existingBranchPath || createBranchPath(undefined, conditionNodeId, branchType as 'yes' | 'no');
    const parentConditions = getParentConditions(branchPath);
    const level = getBranchLevel(branchPath);

    // Save enhanced context for modal
    setConditionBranchInfo({
      conditionNodeId,
      branchType: branchType as 'yes' | 'no',
      placeholderNodeId,
      branchPath,
      level,
      parentConditions
    });

    console.log('🔍 🎯 Setting branch info and opening action modal:', {
      conditionNodeId,
      branchType,
      placeholderNodeId,
      branchPath,
      level,
      parentConditions
    });

    // ✅ CRITICAL FIX: Open the action modal after setting branch info
    setShowActionModal(true);
    console.log('🔍 🎯 Action modal state set to TRUE - should now be open for branch:', branchType);
  }, [createBranchPath, getParentConditions, getBranchLevel]);


  // Handle node insertion with proper conditional flow shifting
  const handleNodeInsertion = useCallback((afterNodeIndex: number, nodeType: string, nodeData: NodeData) => {
    console.log('🔍 handleNodeInsertion called:', {
      afterNodeIndex,
      nodeType,
      nodeData: nodeData.label,
      currentNodesCount: nodes.length,
      currentNodes: nodes.map(n => `${n.id} (${n.type})`)
    });

    const isConditionNode = nodeType === 'condition' || nodeData.type === 'condition';
    const actualNodeType = isConditionNode ? 'condition' : nodeType;
    const actualNodeData = isConditionNode
      ? { ...nodeData, branchNodes: { branch1: [], otherwise: [] }, isConfigured: false }
      : nodeData;

    // 🔑 GENERATE ALL IDs UPFRONT - This is the key fix
    const timestamp = Date.now();
    const nodeId = `${actualNodeType}-${timestamp}`;
    const yesId = `placeholder-yes-${timestamp}`;
    const noId = `placeholder-no-${timestamp}`;

    const newNode: Node = {
      id: nodeId,
      type: actualNodeType,
      position: { x: 0, y: 0 },
      data: {
        ...actualNodeData,
        label: nodeData.label,
        openTriggerModal: actualNodeType === 'trigger' ? () => setShowTriggerModal(true) : undefined,
        isConfigured: false,
        onDelete: isConditionNode ? () => handleConditionNodeDeletion(nodeId) : () => handleNodeDeletion(nodeId),
        onDuplicate: () => {
          // Inline duplicate logic to avoid dependency issues
          console.log('🔍 Duplicating node:', nodeId);
          setTimeout(() => {
            const currentNodes = useWorkflowStore.getState().nodes;
            const originalNode = currentNodes.find(n => n.id === nodeId);
            if (originalNode) {
              const duplicateId = `${originalNode.type}-${Date.now()}`;
              const duplicateNode: Node = {
                id: duplicateId,
                type: originalNode.type,
                position: { x: 0, y: 0 },
                data: {
                  ...originalNode.data,
                  isConfigured: false,
                  onDelete: originalNode.type === 'condition'
                    ? () => handleConditionNodeDeletion(duplicateId)
                    : () => handleNodeDeletion(duplicateId),
                },
              };
              
              useWorkflowStore.getState().setNodes((nds) => {
                const originalIndex = nds.findIndex(n => n.id === nodeId);
                if (originalIndex !== -1) {
                  const newNodes = [...nds];
                  newNodes.splice(originalIndex + 1, 0, duplicateNode);
                  return newNodes;
                }
                return nds;
              });
            }
          }, 50);
        },
        ...(isConditionNode && {
          yesPlaceholderId: yesId,
          noPlaceholderId: noId,
        }),
      },
    };

    setNodes((nds) => {
      console.log('🔍 Current nodes array:', nds.map(n => `${n.id} (${n.type})`));
      console.log('🔍 afterNodeIndex:', afterNodeIndex);

      const newNodes = [...nds];
      let safeIndex = afterNodeIndex;

      // 🛡 Ensure the index is valid (within bounds)
      if (safeIndex < 0 || safeIndex >= newNodes.length) {
        const triggerNode = newNodes.find(n => n.type === 'trigger');
        if (triggerNode) {
          safeIndex = newNodes.findIndex(n => n.id === triggerNode.id);
        } else {
          console.warn('No trigger node found. Inserting at start.');
          safeIndex = -1;
        }
      }

      const previousNode = safeIndex >= 0 ? newNodes[safeIndex] : newNodes.find(n => n.type === 'trigger');
      const nextNode = newNodes[safeIndex + 1];

      console.log('🔍 safeIndex:', safeIndex);
      console.log('🔍 previousNode:', previousNode?.id, previousNode?.type);
      console.log('🔍 nextNode:', nextNode?.id, nextNode?.type);

      // 🎯 GHOST NODE HANDLING: Check if the nextNode is a ghost node
      if (nextNode && nextNode.type === 'ghost' && isConditionNode) {
        console.log('🔍 🎯 GHOST NODE DETECTED: Simply deleting ghost node');
        console.log('🔍 🎯 Ghost node ID to delete:', nextNode.id);
        
        // Simply remove the ghost node from the array - that's it!
        const filteredNodes = newNodes.filter(n => n.id !== nextNode.id);
        
        // Insert the new conditional node normally
        filteredNodes.splice(safeIndex + 1, 0, newNode);

        // Create both Yes and No placeholders as normal
        const yesPlaceholder: Node = {
          id: yesId,
          type: 'placeholder',
          position: { x: 0, y: 0 },
          width: nodeWidth,
          height: nodeWidth,
          data: {
            label: 'Add Action',
            isConfigured: false,
            branchType: 'yes' as const,
            conditionNodeId: newNode.id,
            handleAddNodeToBranch
          }
        };

        const noPlaceholder: Node = {
          id: noId,
          type: 'placeholder',
          position: { x: 0, y: 0 },
          width: nodeWidth,
          height: nodeWidth,
          data: {
            label: 'Add Action',
            isConfigured: false,
            branchType: 'no' as const,
            conditionNodeId: newNode.id,
            handleAddNodeToBranch
          }
        };

        // Add placeholders after the conditional node
        filteredNodes.splice(safeIndex + 2, 0, yesPlaceholder, noPlaceholder);

        // Store placeholder IDs for edge creation
        newNode.data.yesPlaceholderId = yesId;
        newNode.data.noPlaceholderId = noId;

        console.log('🔍 🎯 Ghost node deleted, conditional node inserted normally');
        return filteredNodes;
      }

      // Insert the new node
      newNodes.splice(safeIndex + 1, 0, newNode);

      if (isConditionNode) {
        // 🔄 CONDITIONAL RESTRUCTURING LOGIC
        // When inserting a condition node, move downstream nodes to the "Yes" branch
        // NOTE: This only runs if we didn't handle a ghost node above

        if (nextNode && nextNode.id !== 'virtual-end' && nextNode.type !== 'ghost') {
          console.log('🔄 Restructuring flow: Moving downstream to Yes branch');

          // Move the nextNode and all downstream nodes to the Yes branch
          // by replacing the Yes placeholder with the actual nextNode
          const yesNodeId = nextNode.id; // Use the existing downstream node as Yes branch

          // Update the nextNode to be part of the Yes branch
          const updatedNextNode = {
            ...nextNode,
            data: {
              ...nextNode.data,
              branchType: 'yes',
              conditionNodeId: newNode.id,
              // 🔄 Add plus button functionality for action nodes moved to Yes branch
              showBottomPlus: nextNode.type === 'action' ? true : nextNode.data.showBottomPlus,
              onInsertBelow: nextNode.type === 'action' ? (nodeId: string) => {
                console.log('🔍 Insert below clicked in Yes branch:', nodeId);
                setConditionBranchInfo({
                  conditionNodeId: newNode.id,
                  branchType: 'yes',
                  placeholderNodeId: `after-${nodeId}`
                });
                setShowActionModal(true);
              } : nextNode.data.onInsertBelow,
            }
          };

          // Replace the nextNode in the array with the updated version
          const nextNodeIndex = newNodes.findIndex(n => n.id === nextNode.id);
          if (nextNodeIndex !== -1) {
            newNodes[nextNodeIndex] = updatedNextNode;
          }

          // Create only the No placeholder (Yes branch uses the existing downstream)
          const noPlaceholder: Node = {
            id: noId,
            type: 'placeholder',
            position: { x: 0, y: 0 },
            width: nodeWidth,
            height: nodeWidth,
            data: {
              label: 'Add Action',
              isConfigured: false,
              branchType: 'no' as const,
              conditionNodeId: newNode.id,
              handleAddNodeToBranch
            }
          };

          // Add only the No placeholder
          newNodes.splice(safeIndex + 2, 0, noPlaceholder);

          // Store the Yes node ID for edge creation
          newNode.data.yesPlaceholderId = yesNodeId;
          newNode.data.noPlaceholderId = noId;

        } else {
          // No downstream nodes - create both placeholders as usual
          console.log('🔍 Creating both Yes and No placeholders (no downstream)');

          const yesPlaceholder: Node = {
            id: yesId,
            type: 'placeholder',
            position: { x: 0, y: 0 },
            width: nodeWidth,
            height: nodeWidth,
            data: {
              label: 'Add Action',
              isConfigured: false,
              branchType: 'yes' as const,
              conditionNodeId: newNode.id,
              handleAddNodeToBranch
            }
          };

          const noPlaceholder: Node = {
            id: noId,
            type: 'placeholder',
            position: { x: 0, y: 0 },
            width: nodeWidth,
            height: nodeWidth,
            data: {
              label: 'Add Action',
              isConfigured: false,
              branchType: 'no' as const,
              conditionNodeId: newNode.id,
              handleAddNodeToBranch
            }
          };

          newNodes.splice(safeIndex + 2, 0, yesPlaceholder, noPlaceholder);

          // Store placeholder IDs for edge creation
          newNode.data.yesPlaceholderId = yesId;
          newNode.data.noPlaceholderId = noId;
        }
      }

      return newNodes;
    });

    // Handle edges
    setEdges((eds) => {
      console.log('🔍 Current edges before insertion:', eds);
      let newEdges = [...eds];

      const previousNode = nodes[afterNodeIndex];
      const nextNode = nodes[afterNodeIndex + 1];

      // 🎯 GHOST NODE EDGE HANDLING: Remove edges connecting to the ghost node
      if (nextNode && nextNode.type === 'ghost' && isConditionNode) {
        console.log('🔍 🎯 Removing edges to ghost node:', nextNode.id);
        newEdges = newEdges.filter(edge => edge.target !== nextNode.id);
        
        // Add edge from previous node to new conditional node
        if (previousNode) {
          newEdges.push({
            id: `edge-${previousNode.id}-${newNode.id}`,
            source: previousNode.id,
            target: newNode.id,
            type: 'flowEdge',
            animated: false,
            data: {
              onOpenActionModal: (insertIndex: number) => {
                openActionModal(insertIndex);
              },
              index: afterNodeIndex,
            },
          });
        }

        // Create condition edges to placeholders
        const yesTargetId = String(newNode.data.yesPlaceholderId || '');
        const noTargetId = String(newNode.data.noPlaceholderId || '');

        if (yesTargetId) {
          newEdges.push({
            id: `edge-${newNode.id}-yes`,
            source: newNode.id,
            sourceHandle: 'yes',
            target: yesTargetId,
            type: 'condition',
            label: 'Yes',
            data: { branchType: 'yes' },
          });
        }

        if (noTargetId) {
          newEdges.push({
            id: `edge-${newNode.id}-no`,
            source: newNode.id,
            sourceHandle: 'no',
            target: noTargetId,
            type: 'condition',
            label: 'No',
            data: { branchType: 'no' },
          });
        }

        console.log('🔍 🎯 Ghost node edge handling completed');
        return newEdges;
      }

      // Regular insertion logic (non-ghost)
      if (previousNode && nextNode) {
        console.log('🔍 Removing edge between:', previousNode.id, '->', nextNode.id);
        newEdges = newEdges.filter(edge =>
          !(edge.source === previousNode.id && edge.target === nextNode.id)
        );
      }

      if (previousNode) {
        const newEdge = {
          id: `edge-${previousNode.id}-${newNode.id}`,
          source: previousNode.id,
          target: newNode.id,
          type: 'flowEdge',
          data: {
            onOpenActionModal: (insertIndex: number) => {
              openActionModal(insertIndex);
            },
            index: afterNodeIndex,
          },
        };
        console.log('🔍 Adding edge from previous to new:', newEdge);
        newEdges.push(newEdge);
      }

      if (isConditionNode) {
        // 🔄 Create condition edges based on restructuring
        const yesTargetId = String(newNode.data.yesPlaceholderId || '');
        const noTargetId = String(newNode.data.noPlaceholderId || '');

        console.log('🔍 Creating condition edges:', { yesTargetId, noTargetId });

        // Create Yes branch edge
        if (yesTargetId) {
          const yesEdge = {
            id: `edge-${newNode.id}-yes`,
            source: newNode.id,
            sourceHandle: 'yes',
            target: yesTargetId,
            type: 'condition',
            label: 'Yes',
            data: {
              branchType: 'yes',
            },
          };
          newEdges.push(yesEdge);
          console.log('🔍 Added Yes edge:', yesEdge);
        }

        // Create No branch edge
        if (noTargetId) {
          const noEdge = {
            id: `edge-${newNode.id}-no`,
            source: newNode.id,
            sourceHandle: 'no',
            target: noTargetId,
            type: 'condition',
            label: 'No',
            data: {
              branchType: 'no',
            },
          };
          newEdges.push(noEdge);
          console.log('🔍 Added No edge:', noEdge);
        }
      } else if (nextNode) {
        // For non-condition nodes, connect to the next node
        newEdges.push({
          id: `edge-${newNode.id}-${nextNode.id}`,
          source: newNode.id,
          target: nextNode.id,
          type: 'flowEdge',
          animated: false,
          data: {
            onOpenActionModal: (insertIndex: number) => {
              openActionModal(insertIndex);
            },
            index: afterNodeIndex + 1,
          },
        });
      }

      return newEdges;
    });

    toast.success(`${nodeData.label} inserted into workflow!`);

    // ✅ Auto-open configuration panel for all nodes inserted via handleNodeInsertion
    if (nodeType === 'action' || nodeType === 'condition' || !nodeType) {
      console.log('🔍 Auto-opening config for inserted node:', nodeId, nodeData.label);
      setTimeout(() => {
        const currentNodes = useWorkflowStore.getState().nodes;
        const insertedNode = currentNodes.find(n => n.id === nodeId);
        if (insertedNode) {
          console.log('🔍 Found inserted node, setting as selected:', insertedNode.id);
          setSelectedNode(insertedNode);
        } else {
          console.log('❌ Inserted node not found in state:', nodeId);
        }
      }, 100);
    }
  }, [setNodes, setEdges, openActionModal, handleAddNodeToBranch, handleConditionNodeDeletion, handleNodeDeletion, setSelectedNode, nodes]);

  // Fix placeholder nodes after graph operations to set proper handleAddNodeToBranch
  useEffect(() => {
    console.log('🔍 🎯 useEffect running - checking placeholder nodes. Total nodes:', nodes.length);

    // Check if any placeholder nodes need fixing
    const placeholderNodes = nodes.filter(node =>
      node.type === 'placeholder' &&
      node.data?.branchType &&
      node.data?.conditionNodeId
    );

    const brokenPlaceholders = placeholderNodes.filter(node => {
      const hasProperHandler = node.data.handleAddNodeToBranch &&
        typeof node.data.handleAddNodeToBranch === 'function' &&
        !node.data.handleAddNodeToBranch.toString().includes('placeholder clicked') &&
        !node.data.handleAddNodeToBranch.toString().includes('not properly set');
      return !hasProperHandler;
    });

    // ✅ Check for action nodes in branches that need onInsertBelow callbacks
    const branchActionNodes = nodes.filter(node =>
      node.type === 'action' &&
      node.data.branchType &&
      node.data.conditionNodeId &&
      (!node.data.onInsertBelow || typeof node.data.onInsertBelow !== 'function')
    );

    if (brokenPlaceholders.length > 0 || branchActionNodes.length > 0) {
      console.log('🔍 🎯 Found broken nodes:', {
        placeholders: brokenPlaceholders.length,
        branchActions: branchActionNodes.length
      });

      setNodes(currentNodes => {
        return currentNodes.map(node => {
          // Fix placeholder nodes that don't have proper handleAddNodeToBranch
          if (node.type === 'placeholder' && node.data?.branchType && node.data?.conditionNodeId) {
            const hasProperHandler = node.data.handleAddNodeToBranch &&
              typeof node.data.handleAddNodeToBranch === 'function' &&
              !node.data.handleAddNodeToBranch.toString().includes('placeholder clicked') &&
              !node.data.handleAddNodeToBranch.toString().includes('not properly set');

            if (!hasProperHandler) {
              console.log('🔍 🎯 FIXING placeholder node:', node.id, 'Branch type:', node.data.branchType);

              return {
                ...node,
                data: {
                  ...node.data,
                  handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) => {
                    console.log('🔍 🎯 FIXED placeholder clicked - calling handleAddNodeToBranch:', { branchType, placeholderNodeId, conditionNodeId });
                    handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId);
                  }
                }
              };
            }
          }

          // ✅ Fix action nodes in branches that need onInsertBelow callbacks
          if (node.type === 'action' && node.data.branchType && node.data.conditionNodeId) {
            const hasProperCallback = node.data.onInsertBelow && typeof node.data.onInsertBelow === 'function';

            if (!hasProperCallback) {
              console.log('🔍 🎯 FIXING branch action node:', node.id, 'Branch:', node.data.branchType);

              return {
                ...node,
                data: {
                  ...node.data,
                  showBottomPlus: true,
                  onInsertBelow: (nodeId: string) => {
                    console.log('🔍 Insert below clicked in branch:', nodeId, 'Branch:', node.data.branchType);
                    setConditionBranchInfo({
                      conditionNodeId: node.data.conditionNodeId,
                      branchType: node.data.branchType,
                      placeholderNodeId: `after-${nodeId}`,
                      branchPath: node.data.branchPath,
                      level: node.data.level,
                      parentConditions: node.data.parentConditions
                    });
                    setShowActionModal(true);
                  }
                }
              };
            }
          }

          return node;
        });
      });
    } else {
      console.log('🔍 🎯 All nodes are properly configured');
    }
  }, [nodes.length]); // Run when nodes count changes

  const handleConditionBranchNodeDeletion = useCallback((nodeId: string, conditionNodeId: string, branchType: 'yes' | 'no') => {
    console.log('🔍 🎯 Enhanced conditional branch node deletion:', { nodeId, conditionNodeId, branchType });

    // Check if the node being deleted is currently selected and close config panel
    if (selectedNode && selectedNode.id === nodeId) {
      console.log('🔍 Closing config panel for deleted branch node:', nodeId);
      setSelectedNode(null);
    }

    // Check if there are downstream nodes after the node being deleted
    const nodeToDelete = nodes.find(n => n.id === nodeId);
    const outgoingEdge = edges.find(e => e.source === nodeId);
    const hasDownstreamNodes = outgoingEdge && outgoingEdge.target !== 'virtual-end';

    console.log('🔍 🎯 Deletion context:', {
      nodeToDelete: nodeToDelete?.id,
      nodeType: nodeToDelete?.type,
      hasDownstreamNodes,
      outgoingTarget: outgoingEdge?.target
    });

    if (!hasDownstreamNodes && nodeToDelete?.type === 'condition') {
      // If deleting a conditional node with no downstream nodes, we need to create a ghost node
      console.log('🔍 🎯 Deleting conditional node with no downstream - creating ghost node');
      
      // Create a wrapper function that matches the graph method signature
      const graphHandleAddNodeToBranch = (insertionIndex: number, branchType: 'yes' | 'no', conditionNodeId: string, placeholderNodeId: string, action: unknown) => {
        handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId);
      };
      
      // Use graph-based deletion first
      deleteConditionalBranchNode(nodeId, conditionNodeId, branchType, graphHandleAddNodeToBranch);
      
      // After deletion, create a ghost node to replace the placeholder
      setTimeout(() => {
        const currentNodes = useWorkflowStore.getState().nodes;
        const currentEdges = useWorkflowStore.getState().edges;
        
        // Find the placeholder that was created
        const placeholder = currentNodes.find(n => 
          n.type === 'placeholder' && 
          n.data?.branchType === branchType && 
          n.data?.conditionNodeId === conditionNodeId
        );
        
        if (placeholder) {
          console.log('🔍 🎯 Found placeholder, replacing with ghost node:', placeholder.id);
          
          // Create ghost node
          const branchPath = (placeholder.data?.branchPath as string) || `${conditionNodeId}.${branchType}`;
          const ghostNodeId = createGhostNodeId(branchPath);
          
          // Check if ghost node already exists
          const existingGhost = currentNodes.find(n => n.id === ghostNodeId);
          
          if (!existingGhost) {
            const isHorizontal = layoutDirection === 'LR';
            const ghostNode: Node = {
              id: ghostNodeId,
              type: 'ghost',
              position: placeholder.position,
              width: isHorizontal ? 20 : 5,
              height: isHorizontal ? 5 : 20,
              data: {},
            };
            
            // Replace placeholder with ghost node
            useWorkflowStore.getState().setNodes((nds) => {
              return nds.map(n => n.id === placeholder.id ? ghostNode : n);
            });
            
            // Update edges to point to ghost node
            useWorkflowStore.getState().setEdges((eds) => {
              return eds.map(e => {
                if (e.target === placeholder.id) {
                  return {
                    ...e,
                    target: ghostNodeId,
                    data: {
                      ...e.data,
                      onOpenActionModal: () => {
                        setConditionBranchInfo({
                          conditionNodeId: conditionNodeId,
                          branchType: branchType,
                          placeholderNodeId: `after-${e.source}`,
                          branchPath: branchPath,
                        });
                        setShowActionModal(true);
                      },
                    }
                  };
                }
                return e;
              });
            });
            
            console.log('🔍 🎯 Ghost node created and connected successfully');
          }
        }
      }, 100);
    } else {
      // Normal deletion - use graph operations
      console.log('🔍 🎯 Normal conditional branch deletion');
      const graphHandleAddNodeToBranch = (insertionIndex: number, branchType: 'yes' | 'no', conditionNodeId: string, placeholderNodeId: string, action: unknown) => {
        handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId);
      };

      deleteConditionalBranchNode(nodeId, conditionNodeId, branchType, graphHandleAddNodeToBranch);
    }

    toast.success('Branch node deleted successfully!');
    console.log('✅ Enhanced conditional branch node deletion completed');
  }, [deleteConditionalBranchNode, handleAddNodeToBranch, selectedNode, setSelectedNode, nodes, edges, createGhostNodeId, layoutDirection]);

  // Unified delete function that automatically detects node type and calls appropriate deletion
  const handleUnifiedNodeDeletion = useCallback((nodeId: string | number) => {
    const nodeIdStr = String(nodeId);
    console.log('🔍 Unified delete called for node:', nodeIdStr);

    // Don't delete end node or trigger nodes
    if (nodeIdStr === 'virtual-end' || nodeIdStr.startsWith('trigger-')) {
      console.log('❌ Cannot delete end node or trigger nodes');
      return;
    }

    // Find the node in the current nodes array
    const nodeToDelete = nodes.find(n => n.id === nodeIdStr);
    if (!nodeToDelete) {
      console.warn(`Node with ID ${nodeIdStr} not found.`);
      return;
    }

    // Check if this node is connected to a condition branch
    const incomingEdge = edges.find(edge => edge.target === nodeIdStr);

    if (incomingEdge) {
      // Find the source node of the incoming edge
      const sourceNode = nodes.find(n => n.id === incomingEdge.source);

      // Check if the source is a condition node
      if (sourceNode && sourceNode.type === 'condition') {
        // This is a branch node - determine which branch (yes/no)
        const branchType = String(incomingEdge.label)?.toLowerCase() === 'yes' ? 'yes' :
          String(incomingEdge.label)?.toLowerCase() === 'no' ? 'no' :
            incomingEdge.sourceHandle === 'yes' ? 'yes' : 'no';

        console.log('🔍 Detected branch node deletion:', { nodeId: nodeIdStr, conditionNodeId: sourceNode.id, branchType });
        handleConditionBranchNodeDeletion(nodeIdStr, sourceNode.id, branchType as 'yes' | 'no');
        return;
      }
    }

    // Check if this IS a condition node itself
    if (nodeToDelete.type === 'condition') {
      console.log('🔍 Detected condition node deletion:', nodeIdStr);
      handleConditionNodeDeletion(nodeIdStr);
      return;
    }

    // Default to regular node deletion
    console.log('🔍 Detected regular node deletion:', nodeIdStr);
    handleNodeDeletion(nodeIdStr);
  }, [nodes, edges, handleConditionBranchNodeDeletion, handleConditionNodeDeletion, handleNodeDeletion]);

  // Handle node duplication - creates a copy of the node below the original
  const handleNodeDuplication = useCallback((nodeId: string) => {
    console.log('🔍 Duplicating node:', nodeId);

    const originalNode = nodes.find(n => n.id === nodeId);
    if (!originalNode) {
      console.error('Original node not found:', nodeId);
      return;
    }

    // Generate new ID for the duplicate
    const timestamp = Date.now();
    const duplicateId = `${originalNode.type}-${timestamp}`;

    // Special handling for condition nodes - duplicate goes to Yes branch
    if (originalNode.type === 'condition') {
      console.log('🔍 Duplicating condition node to Yes branch');

      // Find the Yes placeholder for this condition
      const yesPlaceholder = nodes.find(n =>
        n.type === 'placeholder' &&
        n.data?.branchType === 'yes' &&
        n.data?.conditionNodeId === nodeId
      );

      if (yesPlaceholder) {
        // Create new placeholders for the duplicated condition
        const newYesId = `placeholder-yes-${timestamp}`;
        const newNoId = `placeholder-no-${timestamp}`;

        // Create the duplicate condition in the Yes branch
        const duplicateCondition: Node = {
          id: duplicateId,
          type: 'condition',
          position: { x: 0, y: 0 },
          data: {
            ...originalNode.data,
            isConfigured: false,
            branchType: 'yes',
            conditionNodeId: nodeId,
            // Store placeholder IDs for consistent edge creation
            yesPlaceholderId: newYesId,
            noPlaceholderId: newNoId,
            onDelete: () => handleConditionBranchNodeDeletion(duplicateId, nodeId, 'yes'),
            onDuplicate: () => handleNodeDuplication(duplicateId),
          },
        };

        const newYesPlaceholder: Node = {
          id: newYesId,
          type: 'placeholder',
          position: { x: 0, y: 0 },
          width: nodeWidth,
          height: nodeWidth,
          data: {
            label: 'Add Action',
            branchType: 'yes',
            conditionNodeId: duplicateId,
            handleAddNodeToBranch,
          },
        };

        const newNoPlaceholder: Node = {
          id: newNoId,
          type: 'placeholder',
          position: { x: 0, y: 0 },
          width: nodeWidth,
          height: nodeWidth,
          data: {
            label: 'Add Action',
            branchType: 'no',
            conditionNodeId: duplicateId,
            handleAddNodeToBranch,
          },
        };

        // Update nodes - replace Yes placeholder with duplicate condition and add new placeholders
        setNodes((nds) => {
          const filteredNodes = nds.filter(n => n.id !== yesPlaceholder.id);
          return [...filteredNodes, duplicateCondition, newYesPlaceholder, newNoPlaceholder];
        });

        // Update edges
        setEdges((eds) => {
          let newEdges = [...eds];

          // Remove edge to old Yes placeholder
          newEdges = newEdges.filter(edge => edge.target !== yesPlaceholder.id);

          // Add edge from original condition to duplicate condition (Yes branch)
          newEdges.push({
            id: `edge-${nodeId}-yes`,
            source: nodeId,
            sourceHandle: 'yes',
            target: duplicateId,
            type: 'condition',
            label: 'Yes',
            data: { branchType: 'yes' },
          });

          // Add edges from duplicate condition to its placeholders
          newEdges.push({
            id: `edge-${duplicateId}-yes`,
            source: duplicateId,
            sourceHandle: 'yes',
            target: newYesId,
            type: 'condition',
            label: 'Yes',
            data: { branchType: 'yes' },
          });

          newEdges.push({
            id: `edge-${duplicateId}-no`,
            source: duplicateId,
            sourceHandle: 'no',
            target: newNoId,
            type: 'condition',
            label: 'No',
            data: { branchType: 'no' },
          });

          return newEdges;
        });

        toast.success('Condition duplicated to Yes branch!');
        return;
      }
    }

    // Regular node duplication logic (for action nodes)
    const duplicateNode: Node = {
      id: duplicateId,
      type: originalNode.type,
      position: { x: 0, y: 0 }, // Let dagre handle positioning
      data: {
        ...originalNode.data,
        isConfigured: false, // Reset configuration state
        onDelete: () => deleteSingleNode(duplicateId),
        onDuplicate: () => handleNodeDuplication(duplicateId), // Add duplicate handler to new node
      },
    };

    setNodes((nds) => {
      // Find the original node index
      const originalIndex = nds.findIndex(n => n.id === nodeId);
      if (originalIndex === -1) return nds;

      // Insert duplicate after the original node
      const newNodes = [...nds];
      newNodes.splice(originalIndex + 1, 0, duplicateNode);
      return newNodes;
    });

    setEdges((eds) => {
      // Find the outgoing edge from the original node
      const outgoingEdge = eds.find(edge => edge.source === nodeId);

      if (outgoingEdge) {
        // Calculate the correct index for the duplicate node
        const originalNodeIndex = nodes.findIndex(n => n.id === nodeId);
        const duplicateIndex = originalNodeIndex + 1;

        // Create new edge from original to duplicate with correct index
        const originalToDuplicate = {
          id: `edge-${nodeId}-${duplicateId}`,
          source: nodeId,
          target: duplicateId,
          type: 'flowEdge',
          animated: false,
          data: {
            onOpenActionModal: (insertIndex: number) => openActionModal(insertIndex),
            index: duplicateIndex, // Use the correct index for the duplicate
          },
        };

        // Update the outgoing edge to come from duplicate instead with incremented index
        const duplicateToNext = {
          ...outgoingEdge,
          id: `edge-${duplicateId}-${outgoingEdge.target}`,
          source: duplicateId,
          data: {
            ...outgoingEdge.data,
            index: duplicateIndex + 1, // Increment index for the next edge
          },
        };

        // Remove original outgoing edge and add new edges
        const newEdges = eds.filter(edge => edge.id !== outgoingEdge.id);
        newEdges.push(originalToDuplicate, duplicateToNext);

        return newEdges;
      }

      return eds;
    });

    toast.success('Node duplicated successfully!');
  }, [nodes, handleConditionNodeDeletion, handleNodeDeletion, openActionModal, setNodes, setEdges]);


  // Handle insertion logic when a node is inserted between existing nodes
  const handleNodeInsertionInBranch = useCallback((insertionIndex: number, branchType: 'yes' | 'no', conditionNodeId: string, placeholderNodeId: string, action: NodeData) => {
    const timestamp = Date.now();
    const newNodeId = `node-${timestamp}`;
    const newPlaceholderId = `placeholder-next-${timestamp}`;

    setNodes((prevNodes) => {
      const placeholder = prevNodes.find((n) => n.id === placeholderNodeId);
      if (!placeholder) {
        return prevNodes;
      }

      const newNode: Node = {
        id: newNodeId,
        type: 'action',
        position: placeholder.position,
        data: {
          ...action,
          label: action.label,
          isConfigured: false,
          nextPlaceholderId: newPlaceholderId,
          branchType,
          conditionNodeId,
        },
      };

      // Create the next placeholder in the sequence
      const nextPlaceholder: Node = {
        id: newPlaceholderId,
        type: 'placeholder',
        position: { x: placeholder.position.x, y: placeholder.position.y + 100 },
        width: nodeWidth,
        height: nodeWidth,
        data: {
          label: 'Add Action',
          branchType,
          conditionNodeId,
          handleAddNodeToBranch,
        },
      };

      // Remove the old placeholder and add the new node and next placeholder
      let updatedNodes = prevNodes.filter((n) => n.id !== placeholderNodeId);
      updatedNodes.push(newNode, nextPlaceholder);

      // Update positions of subsequent nodes in the branch
      const branchNodes = updatedNodes.filter(node =>
        node.data.branchType === branchType &&
        node.data.conditionNodeId === conditionNodeId
      );

      // Reposition nodes that come after the insertion point
      branchNodes.forEach((node, index) => {
        if (index > insertionIndex) {
          node.position.y += 100; // Shift down to make room
        }
      });

      return updatedNodes;
    });

    setEdges((prevEdges) => {
      // Find the edge that was pointing to the old placeholder
      const incomingEdge = prevEdges.find(edge => edge.target === placeholderNodeId);

      // Update edges
      let updatedEdges = prevEdges.map((edge) => {
        if (edge.target === placeholderNodeId) {
          // Point the incoming edge to the new node
          return { ...edge, target: newNodeId };
        }
        return edge;
      });

      // Add the new edge from the new node to the next placeholder
      updatedEdges.push({
        id: `edge-${newNodeId}-${newPlaceholderId}`,
        source: newNodeId,
        target: newPlaceholderId,
        type: 'flowEdge',
        animated: false,
        data: {
          onOpenActionModal: () => {
            setConditionBranchInfo({
              conditionNodeId,
              branchType,
              placeholderNodeId: newPlaceholderId,
              insertionIndex: insertionIndex + 1,
            });
            setShowActionModal(true);
          },
          index: insertionIndex + 1,
          branchType,
          conditionNodeId,
        },
      });

      // Update indices of subsequent edges in the branch
      updatedEdges = updatedEdges.map(edge => {
        if (edge.data?.branchType === branchType &&
          edge.data?.conditionNodeId === conditionNodeId &&
          typeof edge.data?.index === 'number' &&
          edge.data.index > insertionIndex) {
          return {
            ...edge,
            data: {
              ...edge.data,
              index: edge.data.index + 1,
            },
          };
        }
        return edge;
      });

      return updatedEdges;
    });
  }, [setNodes, setEdges, setConditionBranchInfo, setShowActionModal, handleAddNodeToBranch]);

  const handleActionSelection = useCallback((action: NodeData) => {
    try {
      const isCondition = action.type === 'condition';

      console.log('🔍 🎯 handleActionSelection called:', {
        action: action.label,
        type: action.type,
        conditionBranchInfo,
        actionInsertIndex,
        showActionModal
      });

      // ✅ VALIDATION: Check if Remove Workflow node can be placed at this position
      if (isRemoveWorkflowNode(action.id)) {
        const canPlace = canPlaceRemoveWorkflowNode(actionInsertIndex || 0, conditionBranchInfo);

        if (!canPlace) {
          toast.error('Remove Workflow node can only be added at the end of the main flow or at the end of a conditional branch');
          console.log('❌ Remove Workflow placement blocked - invalid position');
          return;
        }

        console.log('✅ Remove Workflow placement validated - proceeding');
      }

      if (conditionBranchInfo) {
        const { placeholderNodeId, branchType, conditionNodeId, branchPath } = conditionBranchInfo;
        console.log('🔍 🎯 Processing conditional branch insertion:', { placeholderNodeId, branchType, conditionNodeId, branchPath });

        // ✅ Handle insertion after existing nodes in branches
        if (placeholderNodeId.startsWith('after-')) {
          const sourceNodeId = placeholderNodeId.replace('after-', '');
          console.log('🔍 Inserting node after existing node:', sourceNodeId);

          const timestamp = Date.now();
          const newNodeId = `node-${timestamp}`;
          const yesId = `placeholder-yes-${timestamp}`;
          const noId = `placeholder-no-${timestamp}`;

          setNodes((prevNodes) => {
            const sourceNode = prevNodes.find(n => n.id === sourceNodeId);
            if (!sourceNode) {
              console.error('Source node not found:', sourceNodeId);
              return prevNodes;
            }

            // Find the next node after the source node (if any) BEFORE creating the new node
            const sourceToNextEdge = edges.find(edge => edge.source === sourceNodeId);
            const nextNodeId = sourceToNextEdge?.target;
            const nextNode = nextNodeId ? prevNodes.find(n => n.id === nextNodeId) : null;

            const newNode: Node = {
              id: newNodeId,
              type: isCondition ? 'condition' : 'action',
              position: { x: 0, y: 0 }, // Let dagre handle positioning
              data: {
                ...action,
                label: action.label,
                isConfigured: false,
                branchType,
                conditionNodeId,
                branchPath,
                level: conditionBranchInfo?.level,
                parentConditions: conditionBranchInfo?.parentConditions,
                onDelete: isCondition
                  ? () => handleConditionNodeDeletion(newNodeId)
                  : () => handleConditionBranchNodeDeletion(newNodeId, conditionNodeId, branchType),
                onDuplicate: () => handleNodeDuplication(newNodeId),
                // ✅ Store placeholder IDs for conditional nodes - determine targets upfront
                ...(isCondition && {
                  yesPlaceholderId: nextNode && nextNode.id !== 'virtual-end' ? nextNode.id : yesId,
                  noPlaceholderId: noId,
                }),
              },
            };

            // ✅ If it's a conditional node, handle downstream nodes like main flow insertion
            if (isCondition) {
              // Create nested branch paths for the new condition
              const newYesBranchPath = createBranchPath(branchPath, newNodeId, 'yes');
              const newNoBranchPath = createBranchPath(branchPath, newNodeId, 'no');
              const newLevel = (conditionBranchInfo?.level || 0) + 1;
              const newParentConditions = [...(conditionBranchInfo?.parentConditions || []), newNodeId];

              // Reuse the sourceToNextEdge and nextNode already declared above

              // 🎯 Check if we have REAL downstream nodes (not ghost nodes)
              const hasRealDownstreamNode = nextNode &&
                                          nextNode.id !== 'virtual-end' &&
                                          nextNode.type !== 'ghost' &&
                                          nextNode.type !== 'placeholder';

              if (hasRealDownstreamNode) {
                // ✅ Move only REAL downstream nodes (action/condition) to the Yes branch
                console.log('🔍 Moving REAL downstream node to Yes branch:', nextNode.id, nextNode.type);
                const updatedNextNode = {
                  ...nextNode,
                  data: {
                    ...nextNode.data,
                    branchType: 'yes',
                    conditionNodeId: newNodeId,
                    branchPath: newYesBranchPath,
                    level: newLevel,
                    parentConditions: newParentConditions,
                    onDelete: nextNode.type === 'condition'
                      ? () => handleConditionNodeDeletion(nextNode.id)
                      : () => handleConditionBranchNodeDeletion(nextNode.id, newNodeId, 'yes'),
                  }
                };

                // Update the next node in the array
                const nextNodeIndex = prevNodes.findIndex(n => n.id === nextNode.id);
                if (nextNodeIndex !== -1) {
                  prevNodes[nextNodeIndex] = updatedNextNode;
                }
              }

              // 🎯 ALWAYS delete ghost nodes if they exist
              if (nextNode && nextNode.type === 'ghost') {
                console.log('🔍 🎯 DELETING ghost node for conditional insertion:', nextNode.id);
                const ghostNodeIndex = prevNodes.findIndex(n => n.id === nextNode.id);
                if (ghostNodeIndex !== -1) {
                  prevNodes.splice(ghostNodeIndex, 1);
                }
              }

                // ✅ Update ALL downstream nodes to have Yes branch context
                // We need to find downstream nodes by traversing the current edge structure
                const findDownstreamNodes = (startNodeId: string, currentEdges: typeof edges) => {
                  const downstreamNodes: string[] = [];
                  const visited = new Set<string>();
                  const queue = [startNodeId];

                  while (queue.length > 0) {
                    const currentNodeId = queue.shift()!;
                    if (visited.has(currentNodeId)) continue;
                    visited.add(currentNodeId);

                    // Find all outgoing edges from current node
                    const outgoingEdges = currentEdges.filter(edge => edge.source === currentNodeId);
                    outgoingEdges.forEach(edge => {
                      if (edge.target !== 'virtual-end' && !visited.has(edge.target)) {
                        downstreamNodes.push(edge.target);
                        queue.push(edge.target);
                      }
                    });
                  }

                  return downstreamNodes;
                };

                // Find all downstream nodes from the next node
                // Reuse the sourceToNextEdge already declared above
                if (sourceToNextEdge) {
                  const downstreamNodeIds = findDownstreamNodes(sourceToNextEdge.target, edges);

                  // Update all downstream nodes to be in the Yes branch
                  downstreamNodeIds.forEach(nodeId => {
                    const nodeIndex = prevNodes.findIndex(n => n.id === nodeId);
                    if (nodeIndex !== -1) {
                      prevNodes[nodeIndex] = {
                        ...prevNodes[nodeIndex],
                        data: {
                          ...prevNodes[nodeIndex].data,
                          branchType: 'yes',
                          conditionNodeId: newNodeId,
                          branchPath: newYesBranchPath,
                          level: newLevel,
                          parentConditions: newParentConditions,
                          onDelete: prevNodes[nodeIndex].type === 'condition'
                            ? () => handleConditionNodeDeletion(prevNodes[nodeIndex].id)
                            : () => handleConditionBranchNodeDeletion(prevNodes[nodeIndex].id, newNodeId, 'yes'),
                        }
                      };
                    }
                  });
                }

              if (hasRealDownstreamNode) {
                // Create only the No placeholder (Yes branch uses existing downstream)
                const noPlaceholder: Node = {
                  id: noId,
                  type: 'placeholder',
                  position: { x: 0, y: 0 }, // Let dagre handle positioning
                  width: nodeWidth,
                  height: nodeWidth,
                  data: {
                    label: 'Add Node',
                    branchType: 'no',
                    conditionNodeId: newNodeId,
                    branchPath: newNoBranchPath,
                    level: newLevel,
                    parentConditions: newParentConditions,
                    handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) =>
                      handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId, newNoBranchPath),
                  },
                };

                return [...prevNodes, newNode, noPlaceholder];
              } else {
                // No downstream nodes - create both Yes and No placeholders
                const yesPlaceholder: Node = {
                  id: yesId,
                  type: 'placeholder',
                  position: { x: 0, y: 0 }, // Let dagre handle positioning
                  width: nodeWidth,
                  height: nodeWidth,
                  data: {
                    label: 'Add Node',
                    branchType: 'yes',
                    conditionNodeId: newNodeId,
                    branchPath: newYesBranchPath,
                    level: newLevel,
                    parentConditions: newParentConditions,
                    handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) =>
                      handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId, newYesBranchPath),
                  },
                };

                const noPlaceholder: Node = {
                  id: noId,
                  type: 'placeholder',
                  position: { x: 0, y: 0 }, // Let dagre handle positioning
                  width: nodeWidth,
                  height: nodeWidth,
                  data: {
                    label: 'Add Node',
                    branchType: 'no',
                    conditionNodeId: newNodeId,
                    branchPath: newNoBranchPath,
                    level: newLevel,
                    parentConditions: newParentConditions,
                    handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) =>
                      handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId, newNoBranchPath),
                  },
                };

                return [...prevNodes, newNode, yesPlaceholder, noPlaceholder];
              }
            }

            return [...prevNodes, newNode];
          });

          setEdges((prevEdges) => {
            // Find the edge from source to next node
            const sourceToNextEdge = prevEdges.find(edge => edge.source === sourceNodeId);

            if (!sourceToNextEdge) {
              console.warn('No outgoing edge found for source node:', sourceNodeId);
              return prevEdges;
            }

            // Remove the old edge and create new ones
            const filteredEdges = prevEdges.filter(edge => edge.id !== sourceToNextEdge.id);

            const newEdges = [
              ...filteredEdges,
              // Edge from source to new node
              {
                id: `edge-${sourceNodeId}-${newNodeId}`,
                source: sourceNodeId,
                target: newNodeId,
                type: 'flowEdge',
                animated: false,
                data: {
                  onOpenActionModal: () => {
                    setConditionBranchInfo({
                      conditionNodeId,
                      branchType,
                      placeholderNodeId: `after-${sourceNodeId}`,
                    });
                    setShowActionModal(true);
                  },
                  branchType,
                  conditionNodeId,
                },
              }
            ];

            // ✅ Handle conditional vs action node differently
            if (isCondition) {
              // For conditional nodes, we need to get the updated node to access stored target IDs
              // Since we can't access the updated nodes state here, we'll reconstruct the logic

              // 🎯 CONDITIONAL NODES: Only connect to REAL downstream nodes, not ghost nodes
              const downstreamNodeId = sourceToNextEdge.target;
              const downstreamNode = downstreamNodeId ? nodes.find(n => n.id === downstreamNodeId) : null;
              const hasRealDownstreamNode = downstreamNode &&
                                          downstreamNode.type !== 'ghost' &&
                                          downstreamNode.type !== 'placeholder' &&
                                          downstreamNodeId !== 'virtual-end';

              // For conditional nodes: Yes branch connects to real nodes only, ghost nodes are deleted
              const actualYesTargetId = hasRealDownstreamNode ? downstreamNodeId : yesId;
              const actualNoTargetId = noId;

              console.log(`🔍 Creating condition edges: ${newNodeId} -> ${actualYesTargetId} (Yes), ${newNodeId} -> ${actualNoTargetId} (No)`);

              // Create Yes edge (either to existing downstream node or placeholder)
              newEdges.push({
                id: `edge-${newNodeId}-yes`,
                source: newNodeId,
                sourceHandle: 'yes',
                target: actualYesTargetId,
                type: 'condition',
                label: 'Yes',
                data: { branchType: 'yes' },
              });

              // Create No edge (always to placeholder)
              newEdges.push({
                id: `edge-${newNodeId}-no`,
                source: newNodeId,
                sourceHandle: 'no',
                target: actualNoTargetId,
                type: 'condition',
                label: 'No',
                data: { branchType: 'no' },
              });
            } else {
              // For action nodes, create edge to next node
              newEdges.push({
                id: `edge-${newNodeId}-${sourceToNextEdge.target}`,
                source: newNodeId,
                target: sourceToNextEdge.target,
                type: 'flowEdge',
                animated: false,
                data: {
                  onOpenActionModal: () => {
                    setConditionBranchInfo({
                      conditionNodeId,
                      branchType,
                      placeholderNodeId: `after-${newNodeId}`,
                    });
                    setShowActionModal(true);
                  },
                  branchType,
                  conditionNodeId,
                },
              });
            }

            return newEdges;
          });

          toast.success(`${action.label} inserted into ${branchType} branch!`);
          setConditionBranchInfo(null);
          setShowActionModal(false);
          return;
        }

        // 🔑 GENERATE ALL IDs UPFRONT - This is the key fix
        const timestamp = Date.now();
        const newNodeId = `node-${timestamp}`;
        const yesId = `placeholder-yes-${timestamp}`;
        const noId = `placeholder-no-${timestamp}`;

        setNodes((prevNodes) => {
          const placeholder = prevNodes.find((n) => n.id === placeholderNodeId);
          if (!placeholder) {
            console.error('Placeholder not found:', placeholderNodeId);
            return prevNodes;
          }

          const newNode: Node = {
            id: newNodeId,
            type: isCondition ? 'condition' : 'action',
            position: placeholder.position,
            data: {
              ...action,
              label: action.label,
              isConfigured: false,
              branchType,
              conditionNodeId,
              branchPath,
              level: conditionBranchInfo?.level,
              parentConditions: conditionBranchInfo?.parentConditions,
              onDelete: isCondition
                ? () => handleConditionNodeDeletion(newNodeId)
                : () => handleConditionBranchNodeDeletion(newNodeId, conditionNodeId, branchType),
              onDuplicate: () => handleNodeDuplication(newNodeId),
              // Store the placeholder IDs for edge creation
              ...(isCondition && {
                yesPlaceholderId: yesId,
                noPlaceholderId: noId,
              }),
            },
          };

          // Remove the placeholder that's being replaced
          const filteredNodes = prevNodes.filter((n) => n.id !== placeholderNodeId);
          const updatedNodes = [...filteredNodes, newNode];

          // ✅ Special handling for Remove Workflow nodes in branches
          if (isRemoveWorkflowNode(action.id)) {
            console.log('🔍 Adding Remove Workflow node in branch - no ghost node connection');
            // For Remove Workflow nodes in branches, don't add any placeholders or ghost connections
            // They should be terminal nodes in the branch
            return updatedNodes;
          }

          // If it's a condition node, add Yes/No placeholders
          if (isCondition) {
            // Create nested branch paths for the new condition
            const newYesBranchPath = createBranchPath(branchPath, newNodeId, 'yes');
            const newNoBranchPath = createBranchPath(branchPath, newNodeId, 'no');
            const newLevel = (conditionBranchInfo?.level || 0) + 1;
            const newParentConditions = [...(conditionBranchInfo?.parentConditions || []), newNodeId];

            const yesPlaceholder: Node = {
              id: yesId, // ✅ Use the same ID generated above
              type: 'placeholder',
              position: { x: 0, y: 0 }, // Let dagre handle positioning
              width: nodeWidth,
              height: nodeWidth,
              data: {
                label: 'Add Action',
                branchType: 'yes',
                conditionNodeId: newNodeId,
                branchPath: newYesBranchPath,
                level: newLevel,
                parentConditions: newParentConditions,
                handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) =>
                  handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId, newYesBranchPath),
              },
            };

            const noPlaceholder: Node = {
              id: noId, // ✅ Use the same ID generated above
              type: 'placeholder',
              position: { x: 0, y: 0 }, // Let dagre handle positioning
              width: nodeWidth,
              height: nodeWidth,
              data: {
                label: 'Add Action',
                branchType: 'no',
                conditionNodeId: newNodeId,
                branchPath: newNoBranchPath,
                level: newLevel,
                parentConditions: newParentConditions,
                handleAddNodeToBranch: (branchType: string, placeholderNodeId: string, conditionNodeId: string) =>
                  handleAddNodeToBranch(branchType, placeholderNodeId, conditionNodeId, newNoBranchPath),
              },
            };

            return [...updatedNodes, yesPlaceholder, noPlaceholder];
          } else {
            // ✅ For action nodes in branches, create a ghost node
            const ghostNodeId = createGhostNodeId(branchPath || `${conditionNodeId}.${branchType}`);

            // Check if ghost node already exists
            const existingGhostNode = prevNodes.find(node =>
              node.type === 'ghost' && node.id === ghostNodeId
            );

            if (!existingGhostNode) {
              // Use dynamic dimensions based on layout direction
              const isHorizontal = layoutDirection === 'LR';
              const ghostNode: Node = {
                id: ghostNodeId,
                type: 'ghost',
                position: { x: 0, y: 0 }, // Let dagre handle positioning
                width: isHorizontal ? 20 : 5,   // Wider for horizontal
                height: isHorizontal ? 5 : 20,  // Taller for vertical
                data: {},
              };

              // Store ghost node ID in the action node's data
              newNode.data.ghostNodeId = ghostNodeId;

              return [...updatedNodes, ghostNode];
            }

            // Store ghost node ID in the action node's data
            newNode.data.ghostNodeId = ghostNodeId;
            return updatedNodes;
          }
        });

        // Update edges with THE SAME IDs
        setEdges((prevEdges) => {
          // Replace edges targeting the old placeholder with edges targeting the new node
          const updatedEdges = prevEdges.map((edge) => {
            if (edge.target === placeholderNodeId) {
              console.log(`Updating edge from ${edge.source} -> ${placeholderNodeId} to ${edge.source} -> ${newNodeId}`);
              return { ...edge, target: newNodeId };
            }
            return edge;
          });

          if (isCondition) {
            // ✅ Use the EXACT SAME IDs that were generated above
            console.log(`Adding condition edges: ${newNodeId} -> ${yesId} (Yes), ${newNodeId} -> ${noId} (No)`);

            // Add condition edges to the new placeholders
            updatedEdges.push(
              {
                id: `edge-${newNodeId}-yes`,
                source: newNodeId,
                sourceHandle: 'yes',
                target: yesId, // ✅ Same ID as node creation
                type: 'condition',
                label: 'Yes',
                data: { branchType: 'yes' },
              },
              {
                id: `edge-${newNodeId}-no`,
                source: newNodeId,
                sourceHandle: 'no',
                target: noId, // ✅ Same ID as node creation
                type: 'condition',
                label: 'No',
                data: { branchType: 'no' },
              }
            );
          } else {
            // ✅ Special handling for Remove Workflow nodes - don't connect to ghost node
            if (isRemoveWorkflowNode(action.id)) {
              console.log('🔍 Remove Workflow node in branch - no ghost node connection');
              // Remove Workflow nodes in branches should be terminal - no outgoing edges
            } else {
              // ✅ Connect regular action node to ghost node using nested branch path
              const ghostNodeId = createGhostNodeId(branchPath || `${conditionNodeId}.${branchType}`);

              // Create edge from new action node to ghost node
              updatedEdges.push({
                id: `edge-${newNodeId}-${ghostNodeId}`,
                source: newNodeId,
                target: ghostNodeId,
                type: 'flowEdge',
                animated: false,
                data: {
                  onOpenActionModal: () => {
                    // Set up for insertion after this node with full branch context
                    setConditionBranchInfo({
                      conditionNodeId: conditionNodeId,
                      branchType: branchType,
                      placeholderNodeId: `after-${newNodeId}`,
                      branchPath: branchPath,
                      level: conditionBranchInfo?.level,
                      parentConditions: conditionBranchInfo?.parentConditions
                    });
                    setShowActionModal(true);
                  },
                  branchType: branchType,
                  conditionNodeId: conditionNodeId,
                  branchPath: branchPath,
                  level: conditionBranchInfo?.level,
                  parentConditions: conditionBranchInfo?.parentConditions,
                },
              });
            }
          }

          return updatedEdges;
        });

        toast.success(`${action.label} added to ${branchType || 'main'} branch!`);

        // ✅ Auto-open configuration panel for all nodes added to branches
        if (action.type === 'action' || action.type === 'condition' || !action.type) {
          console.log('🔍 Auto-opening config for branch node:', newNodeId, action.label);
          // Find the newly added node and open its config
          setTimeout(() => {
            console.log('🔍 Finding branch node in state:', newNodeId);
            const currentNodes = useWorkflowStore.getState().nodes;
            const addedNode = currentNodes.find(n => n.id === newNodeId);
            if (addedNode) {
              console.log('🔍 Found branch node, setting as selected:', addedNode.id);
              setSelectedNode(addedNode);
            } else {
              console.log('❌ Branch node not found in state:', newNodeId);
            }
          }, 100);
        }

        setConditionBranchInfo(null);
      } else {
        // Handle top-level insertion
        if (actionInsertIndex !== null) {
          handleNodeInsertion(actionInsertIndex, action.type || 'action', action);
          setActionInsertIndex(null);
        } else {
          // ✅ Auto-open configuration panel for all nodes
          const shouldAutoOpen = action.type === 'action' || action.type === 'condition' || !action.type;
          handleNodeSelection(action.type || 'action', action, shouldAutoOpen);
        }
      }
    } catch (error) {
      console.error('Error in handleActionSelection:', error);
      console.error('ConditionBranchInfo:', conditionBranchInfo);
      toast.error('Failed to add action to workflow');
    } finally {
      setShowActionModal(false);
      setActionInsertIndex(null);
      setConditionBranchInfo(null);
    }
  }, [
    conditionBranchInfo,
    handleConditionNodeDeletion,
    handleConditionBranchNodeDeletion,
    setConditionBranchInfo,
    setShowActionModal,
    handleNodeSelection,
    handleNodeInsertion,
    setNodes,
    setEdges,
    actionInsertIndex,
    handleAddNodeToBranch,
    setActionInsertIndex,
    createGhostNodeId,
    createBranchPath,
    edges,
    setSelectedNode // ✅ Keep setSelectedNode dependency
  ]);

  const handleTriggerSelection = useCallback((trigger: NodeData) => {
    // Update the trigger node in the graph store
    const nodeMap = useGraphStore.getState().nodes;
    const triggerNode = Object.values(nodeMap).find(node => node.type === 'trigger');

    if (triggerNode) {
      // Update the trigger node with new data
      const updatedData = {
        ...triggerNode.data,
        ...trigger,
        isConfigured: false, // Still needs configuration
        openTriggerModal: () => setShowTriggerModal(true),
      };

      // Update in graph store
      useGraphStore.getState().updateNode(triggerNode.id, { data: updatedData });

      setShowTriggerModal(false);
      toast.success(`${trigger.label} trigger selected!`);

      // Auto-open configuration panel for the trigger
      setTimeout(() => {
        const updatedNodeFromStore = useGraphStore.getState().nodes[triggerNode.id];
        if (updatedNodeFromStore) {
          setSelectedNode({
            id: updatedNodeFromStore.id,
            type: updatedNodeFromStore.type,
            data: updatedNodeFromStore.data,
            position: updatedNodeFromStore.position
          });
        }
      }, 100);
    }
  }, [setSelectedNode]);

  const onNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    // Don't open config panel for default trigger
    const isDefaultTrigger = node.data?.id === 'trigger-default' || node.data?.label === 'Select Trigger';

    if (!isDefaultTrigger) {
      setSelectedNode(node);
    }
  }, [setSelectedNode]);


  // Handle replace trigger
  const handleReplaceTrigger = useCallback(() => {
    setShowTriggerModal(true);
  }, []);

  // Handle open trigger config
  const handleOpenTriggerConfig = useCallback((node: Node) => {
    setSelectedNode(node);
  }, [setSelectedNode]);

  // Handle reset workflow
  const handleResetWorkflow = useCallback(() => {
    if (window.confirm('Are you sure you want to reset the entire workflow? This action cannot be undone.')) {
      // Reset to initial state with default trigger and virtual end
      const defaultTriggerNode: Node = {
        id: 'trigger-default',
        type: 'trigger',
        position: { x: 0, y: 0 },
        data: {
          label: 'Select Trigger',
          icon: 'Zap',
          description: 'Empty Trigger',
          isConfigured: false,
          openTriggerModal: () => setShowTriggerModal(true),
        },
      };

      const endNode: Node = {
        id: 'virtual-end',
        type: 'end',
        position: { x: 0, y: 0 },
        data: {
          label: 'End',
          id: 'end',
        },
      };

      // Set initial nodes
      setNodes([defaultTriggerNode, endNode]);

      // Set initial edge connecting trigger to end
      const initialEdge = {
        id: 'default-edge',
        source: defaultTriggerNode.id,
        target: 'virtual-end',
        type: 'flowEdge',
        animated: false,
        data: {
          onOpenActionModal: (insertIndex: number) => {
            openActionModal(insertIndex);
          },
          index: 0, // This will be the insert index
        },
      };

      setEdges([initialEdge]);
      setSelectedNode(null);
      toast.success('Workflow reset successfully!');
    }
  }, [setNodes, setEdges, setSelectedNode, openActionModal]);

  // Old zoom controls removed - now using React Flow controls with reset button

  // Panel handlers
  const handleOpenRuns = useCallback(() => {
    setActivePanel(activePanel === 'runs' ? null : 'runs');
  }, [activePanel]);

  const handleOpenVersions = useCallback(() => {
    setActivePanel(activePanel === 'versions' ? null : 'versions');
  }, [activePanel]);

  const handleOpenPublish = useCallback(() => {
    setActivePanel(activePanel === 'publish' ? null : 'publish');
  }, [activePanel]);

  const handleClosePanel = useCallback(() => {
    setActivePanel(null);
  }, []);

  const handleLoadVersion = useCallback((versionId: string) => {
    console.log('Loading version:', versionId);
    // TODO: Implement version loading logic
    toast.success(`Loading version ${versionId}...`);
  }, []);

  const handlePublish = useCallback((publishData: {
    version: string;
    name: string;
    description: string;
    isPublic: boolean;
    environment: 'development' | 'staging' | 'production';
    autoActivate: boolean;
    releaseNotes: string;
  }) => {
    console.log('Publishing workflow:', publishData);
    // TODO: Implement publish logic
    toast.success(`Workflow published as v${publishData.version}`);
  }, []);





  // Fix for the useEffect in WorkflowBuilder component
  useEffect(() => {
    // Initialize with default trigger node if empty
    const defaultTriggerNode = {
      id: 'trigger-default',
      type: 'trigger',
      position: { x: 0, y: 0 }, // Start with some padding from edges
      width: nodeWidth,
      height: nodeHeight,
      data: {
        label: 'Select Trigger',
        icon: 'Zap',
        description: 'Empty Trigger',
        isConfigured: false,
        openTriggerModal: () => setShowTriggerModal(true),
      },
    };

    const endNode = {
      id: 'virtual-end',
      type: 'end',
      position: { x: 0, y: 0 }, // Increased spacing - 300px apart
      data: {
        label: 'End',
        id: 'end',
      },
    };

    setNodes([defaultTriggerNode, endNode]);

    // Set initial edge with proper data structure
    const initialEdge = {
      id: 'default-edge',
      source: defaultTriggerNode.id,
      target: 'virtual-end',
      type: 'flowEdge',
      animated: false,
      data: {
        onOpenActionModal: (insertIndex: number) => {
          console.log('🔍 Plus button clicked from edge, insertIndex:', insertIndex);
          openActionModal(insertIndex);
        },
        index: 0, // This will be the insert index
      },
    };

    console.log('🔍 Initial edge created:', initialEdge);
    setEdges([initialEdge]);
  }, [setNodes, setEdges, openActionModal]);

  // Add keyboard event listener for Escape key to clear copy state
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isCopy) {
        clearCopyState();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isCopy, clearCopyState]);

  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden">
      <WorkflowHeader
        workflowName={workflowName}
        setWorkflowName={setWorkflowName}
        isActive={isActive}
        setIsActive={setIsActive}
        onSave={saveWorkflow}
        onExecute={executeWorkflow}
        onReset={handleResetWorkflow}
        onOpenRuns={handleOpenRuns}
        onOpenVersions={handleOpenVersions}
        onOpenPublish={handleOpenPublish}
      />

      <div className="flex flex-1 overflow-hidden relative">
        <div className="flex-1">
          <WorkFlowCanvas openTriggerModal={() => setShowTriggerModal(true)} />
        </div>

        {/* Old controls removed - now using React Flow controls with reset button */}

        {selectedNode && (
          <div className="fixed inset-x-0 bottom-0 h-1/2 z-50 md:relative md:w-[32rem] lg:w-[36rem] md:h-auto md:inset-auto bg-white border-t md:border-t-0 md:border-l border-gray-200 shadow-lg">
            <div className="h-full overflow-y-auto">
              <NodeConfigPanel
                node={selectedNode}
                onClose={() => setSelectedNode(null)}
                onUpdate={(nodeId, updates) => {
                  // Update node in graph store
                  const nodeMap = useGraphStore.getState().nodes;
                  const existingNode = nodeMap[nodeId];

                  if (existingNode) {
                    const updatedNode = {
                      ...existingNode,
                      data: {
                        ...existingNode.data,
                        ...updates,
                        isConfigured: true // Mark as configured after update
                      }
                    };

                    useGraphStore.getState().updateNode(nodeId, updatedNode);
                    console.log('🔍 Node updated in graph store:', nodeId, updates);
                  }

                  setSelectedNode(null); // Close config panel after update
                }}
                onDelete={(nodeId) => {
                  // ✅ Handle node deletion from config panel
                  const nodeToDelete = nodes.find(n => n.id === nodeId);
                  if (nodeToDelete) {
                    if (nodeToDelete.type === 'condition') {
                      handleConditionNodeDeletion(nodeId);
                    } else if (nodeToDelete.data?.branchType && nodeToDelete.data?.conditionNodeId) {
                      // This is a branch node
                      handleConditionBranchNodeDeletion(nodeId, nodeToDelete.data.conditionNodeId as string, nodeToDelete.data.branchType as 'yes' | 'no');
                    } else {
                      // Regular node
                      handleNodeDeletion(nodeId);
                    }
                  }
                  setSelectedNode(null); // Close config panel after deletion
                }}
              />
            </div>
          </div>
        )}
      </div>
  
      {/* Trigger Category Modal */}
      <TriggerCategoryModal
        isOpen={showTriggerModal}
        onClose={() => setShowTriggerModal(false)}
        onSelectTrigger={handleTriggerSelection}
      />

      {/* Action Category Modal */}
      <ActionCategoryModal
        isOpen={showActionModal}
        onClose={() => {
          setShowActionModal(false);
          setActionInsertIndex(null);
        }}
        onSelectAction={handleActionSelection}
      />

      {/* Branch Selection Modal for Conditional Paste */}
      <BranchSelectionModal
        isOpen={showBranchSelectionModal}
        onClose={() => {
          setShowBranchSelectionModal(false);
          setPendingConditionalPaste(null);
        }}
        onSelectBranch={handleBranchSelection}
        downstreamNodeCount={pendingConditionalPaste?.downstreamNodeCount || 0}
      />



      {/* Slide-out Panels */}
      <RunsPanel
        isOpen={activePanel === 'runs'}
        onClose={handleClosePanel}
      />

      <VersionsPanel
        isOpen={activePanel === 'versions'}
        onClose={handleClosePanel}
        onLoadVersion={handleLoadVersion}
      />

      <PublishPanel
        isOpen={activePanel === 'publish'}
        onClose={handleClosePanel}
        onPublish={handlePublish}
      />
    </div>
  );
};
