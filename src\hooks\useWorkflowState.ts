
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Node, Edge } from '@xyflow/react';

interface WorkflowState {
  // Core workflow state
  selectedNode: Node | null;
  workflowName: string;
  isActive: boolean;
  layoutDirection: 'TB' | 'LR'; // Added layout direction

  // These states are used to store the copied/cut node and edges
  copiedNodes: Node[];
  copiedEdges: Edge[];
  isCopy: boolean;

  // Cut and paste state
  cutNodes: Node[];
  cutEdges: Edge[];
  isCut: boolean;

  // Nodes and edges
  nodes: Node[];
  edges: Edge[];

  // Actions for core state
  setSelectedNode: (node: Node | null) => void;
  setWorkflowName: (name: string) => void;
  setIsActive: (active: boolean) => void;
  setLayoutDirection: (direction: 'TB' | 'LR') => void; // Added setter

  // Actions for nodes and edges
  setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
  setEdges: (edges: Edge[] | ((edges: Edge[]) => Edge[])) => void;
  addNode: (node: Node) => void;
  updateNode: (nodeId: string, data: Record<string, unknown>) => void;
  removeNode: (nodeId: string) => void;
  addEdge: (edge: Edge) => void;
  removeEdge: (edgeId: string) => void;

  // Actions for the copy and paste functionality
  setCopiedNodes: (nodes: Node[]) => void;
  setCopiedEdges: (edges: Edge[]) => void;
  setIsCopy: (isCopy: boolean) => void;

  // Actions for the cut and paste functionality
  setCutNodes: (nodes: Node[]) => void;
  setCutEdges: (edges: Edge[]) => void;
  setIsCut: (isCut: boolean) => void;

  // Force reset copy/cut state
  forceResetCopyState: () => void;
  forceResetCutState: () => void;



  // Utility actions
  clearWorkflow: () => void;
  resetUI: () => void;
}

export const useWorkflowStore = create<WorkflowState>()(
  devtools(
    persist(
      (set) => ({
        // Initial state
        selectedNode: null,
        workflowName: 'My workflow',
        isActive: false,
        layoutDirection: 'TB', // Default to vertical layout
        nodes: [],
        edges: [],

        // Copy and paste states
        copiedNodes: [],
        copiedEdges: [],
        isCopy: false,

        // Cut and paste states
        cutNodes: [],
        cutEdges: [],
        isCut: false,

        // Core state actions
        setSelectedNode: (node) => set({ selectedNode: node }, false, 'setSelectedNode'),
        setWorkflowName: (name) => set({ workflowName: name }, false, 'setWorkflowName'),
        setIsActive: (active) => set({ isActive: active }, false, 'setIsActive'),
        setLayoutDirection: (direction) => set({ layoutDirection: direction }, false, 'setLayoutDirection'),

        // Nodes and edges actions
        setNodes: (nodes) => set((state) => ({
          nodes: typeof nodes === 'function' ? nodes(state.nodes) : nodes
        }), false, 'setNodes'),

        setEdges: (edges) => set((state) => ({
          edges: typeof edges === 'function' ? edges(state.edges) : edges
        }), false, 'setEdges'),

        addNode: (node) => set((state) => ({
          nodes: [...state.nodes, node]
        }), false, 'addNode'),

        updateNode: (nodeId, data) => set((state) => ({
          nodes: state.nodes.map(node =>
            node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
          ),
          // Update selected node if it's the one being updated
          selectedNode: state.selectedNode?.id === nodeId
            ? { ...state.selectedNode, data: { ...state.selectedNode.data, ...data } }
            : state.selectedNode
        }), false, 'updateNode'),

        removeNode: (nodeId) => set((state) => ({
          nodes: state.nodes.filter(node => node.id !== nodeId),
          edges: state.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId),
          selectedNode: state.selectedNode?.id === nodeId ? null : state.selectedNode
        }), false, 'removeNode'),

        addEdge: (edge) => set((state) => ({
          edges: [...state.edges, edge]
        }), false, 'addEdge'),

        removeEdge: (edgeId) => set((state) => ({
          edges: state.edges.filter(edge => edge.id !== edgeId)
        }), false, 'removeEdge'),


        // Copy and paste actions
        setCopiedNodes: (nodes) => set({ copiedNodes: nodes }, false, 'setCopiedNodes'),
        setCopiedEdges: (edges) => set({ copiedEdges: edges }, false, 'setCopiedEdges'),
        setIsCopy: (isCopy) => set({ isCopy: isCopy }, false, 'setIsCopy'),

        // Cut and paste actions
        setCutNodes: (nodes) => set({ cutNodes: nodes }, false, 'setCutNodes'),
        setCutEdges: (edges) => set({ cutEdges: edges }, false, 'setCutEdges'),
        setIsCut: (isCut) => set({ isCut: isCut }, false, 'setIsCut'),

        // Utility actions
        clearWorkflow: () => set({
          nodes: [],
          edges: [],
          selectedNode: null,
          workflowName: 'My workflow',
          isActive: false
        }, false, 'clearWorkflow'),

        resetUI: () => set({
          selectedNode: null,
        }, false, 'resetUI'),

        // Force reset copy/cut state
        forceResetCopyState: () => set({
          isCopy: false,
          copiedNodes: [],
          copiedEdges: []
        }, false, 'forceResetCopyState'),

        forceResetCutState: () => set({
          isCut: false,
          cutNodes: [],
          cutEdges: []
        }, false, 'forceResetCutState'),
      }),
      {
        name: 'workflow-storage',
        partialize: (state) => ({
          workflowName: state.workflowName,
          isActive: state.isActive,
          layoutDirection: state.layoutDirection, // Persist layout direction
          nodes: state.nodes,
          edges: state.edges,
        }),
      }
    ),
    { name: 'workflow-store' }
  )
);

// Legacy hook for backward compatibility during migration
export const useWorkflowState = () => {
  const store = useWorkflowStore();
  return store;
};
