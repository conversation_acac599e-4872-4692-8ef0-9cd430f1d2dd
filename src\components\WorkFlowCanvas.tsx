import { useCallback, useEffect, useState } from 'react';
import { applyNodeChanges, applyEdgeChanges, Background, Controls, ReactFlow, BackgroundVariant } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { ActionNode } from './nodes/ActionNode';
import FlowEdge from './edges/FlowEdge';
import { TriggerNode } from './nodes/TriggerNode';
import EndNode from './nodes/EndNode';
import ConditionNode from './nodes/ConditionNode';
import { useGraphStore } from '../store/useGraphStore';
import { graphToReactFlow } from '../lib/graphToReactFlow';
import { useWorkflowStore } from '../hooks/useWorkflowState';
import ConditionEdge from './edges/ConditionEdge';
import PlaceholderNode from './nodes/PlaceHolderNode';
import { GhostNode } from './nodes/GhostNode';
import { getLayoutedElements } from '../utils/dagreFunction';

export const initializeGraph = () => {
  const triggerId = 'trigger-1';
  const endId = 'end-1';

  useGraphStore.getState().reset();

  useGraphStore.getState().addNode({
    id: triggerId,
    type: 'trigger',
    position: { x: 100, y: 100 },
    data: {
      label: 'Select Trigger',
      isConfigured: false,
    },
    children: [endId],
    parent: undefined,
  });

  useGraphStore.getState().addNode({
    id: endId,
    type: 'endNode',
    position: { x: 100, y: 250 },
    data: {
      label: 'End',
    },
    children: [],
    parent: triggerId,
  });

  console.log('Graph initialized with trigger and end nodes');
};


const nodeTypes = {
  action: ActionNode,
  trigger: TriggerNode,
  endNode: EndNode,
  condition: ConditionNode,
  placeholder: PlaceholderNode,
  ghost: GhostNode,
};

const edgeTypes = {
  flowEdge: FlowEdge,
  condition: ConditionEdge,
};

const WorkFlowCanvas = () => {
  const nodeMap = useGraphStore((state) => state.nodes);
  const addNode = useGraphStore((state) => state.addNode);
  const { selectedNode, setSelectedNode } = useWorkflowStore();
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [showActionModal, setShowActionModal] = useState(false);

  useEffect(() => {
    initializeGraph();
  }, []);

  // Auto-close config panel when selected node is deleted
  useEffect(() => {
    if (selectedNode) {
      // Check if the selected node still exists in the graph
      const nodeExists = Object.values(nodeMap).some(node => node.id === selectedNode.id);
      if (!nodeExists) {
        console.log('🔍 Selected node was deleted, closing config panel');
        setSelectedNode(null);
      }
    }
  }, [nodeMap, selectedNode, setSelectedNode]);

  // Simple node addition function
  const handleAddNode = useCallback((nodeData: any) => {
    const newNodeId = `action-${Date.now()}`;

    // Find the trigger node to connect after it
    const triggerNode = Object.values(nodeMap).find(node => node.type === 'trigger');
    const endNode = Object.values(nodeMap).find(node => node.type === 'endNode');

    if (triggerNode && endNode) {
      // Add the new action node
      addNode({
        id: newNodeId,
        type: 'action',
        position: { x: 100, y: 175 }, // Between trigger and end
        data: {
          label: nodeData.label || 'New Action',
          isConfigured: false,
        },
        children: [endNode.id],
        parent: triggerNode.id,
      });

      // Update trigger to point to new node instead of end
      const updatedTrigger = {
        ...triggerNode,
        children: [newNodeId]
      };

      // Update the trigger node
      useGraphStore.getState().removeNode(triggerNode.id);
      addNode(updatedTrigger);
    }

    setShowActionModal(false);
  }, [nodeMap, addNode]);

  // Update local state when store changes and apply Dagre layout
  useEffect(() => {
    try {
      const { stateNodes, stateEdges } = graphToReactFlow(nodeMap);

      // Find the last node in the main flow (not in branches)
      // Look for nodes that are not in any condition branch
      const nodesInBranches = new Set();
      Object.values(nodeMap).forEach(node => {
        if (node.type === 'condition' && node.branches) {
          node.branches.yes?.forEach(id => nodesInBranches.add(id));
          node.branches.no?.forEach(id => nodesInBranches.add(id));
        }
      });

      // Find the last node in main flow (has no children and not in branches)
      const mainFlowNodes = stateNodes.filter(node =>
        node.type !== 'endNode' &&
        node.type !== 'placeholder' &&
        !nodesInBranches.has(node.id)
      );

      const lastMainFlowNode = mainFlowNodes.find(node => {
        const graphNode = nodeMap[node.id];
        return graphNode && (!graphNode.children || graphNode.children.length === 0 ||
          (graphNode.children.length === 1 && graphNode.children[0] === 'end-1'));
      });


      const shouldHideEndNode = lastMainFlowNode?.type === 'condition';

      const filteredNodes = shouldHideEndNode
        ? stateNodes.filter(node => node.id !== 'end-1')
        : stateNodes;

      const filteredEdges = shouldHideEndNode
        ? stateEdges.filter(edge => edge.target !== 'end-1')
        : stateEdges;

      // Apply Dagre layout
      const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
        filteredNodes,
        filteredEdges,
        'TB' // Vertical layout
      );

      setNodes(layoutedNodes);
      setEdges(layoutedEdges);
    } catch (error) {
      console.error('Error in graphToReactFlow:', error);
    }
  }, [nodeMap]);

  const onNodesChange = useCallback((changes: any[]) => {
    console.log('Node changes:', changes);
    setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot));
  }, []);

  const onEdgesChange = useCallback((changes: any[]) => {
    console.log('Edge changes:', changes);
    setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot));
  }, []);

  // Handle node clicks to open config panel
  const onNodeClick = useCallback((event: React.MouseEvent, node: any) => {
    console.log('🔍 Node clicked:', node.id, node.type);

    // Don't open config for placeholder, ghost, or end nodes
    if (node.type === 'placeholder' || node.type === 'ghost' || node.type === 'endNode') {
      return;
    }

    // For trigger nodes, only open config if they're configured (not default)
    if (node.type === 'trigger') {
      const isDefaultTrigger = node.data?.id === 'trigger-default' || node.data?.label === 'Select Trigger';
      if (isDefaultTrigger) {
        return; // Let the trigger node handle its own modal
      }
    }

    // Open config panel for configured nodes
    setSelectedNode(node);
  }, [setSelectedNode]);

  return (
    <div style={{
      width: '100%',
      height: '100vh',
      overflowY: 'scroll',
      overflowX: 'hidden'
    }}>
      <ReactFlow
        nodeTypes={nodeTypes as any}
        edgeTypes={edgeTypes as any}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClick}
        nodesDraggable={false}
        fitView
        defaultViewport={{ x: 0, y: 80, zoom: 0.75 }}  // ⬅️ Note the x: 0
        minZoom={0.2}
        maxZoom={1.5}
        panOnScroll
        preventScrolling= {false}
      >
        <Background color="#aaa" gap={16} variant={BackgroundVariant.Dots} />
        <Controls />
      </ReactFlow>
    </div>

  );
};

export default WorkFlowCanvas;

